package net.armcloud.paascenter.task.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.cms.manager.cbs.model.bo.CreatePadBO;
import net.armcloud.paascenter.cms.mapper.ConfigurationMapper;
import net.armcloud.paascenter.cms.mapper.TaskRelInstanceDetailMapper;
import net.armcloud.paascenter.cms.model.request.*;
import net.armcloud.paascenter.cms.service.InstanceDetailImageSuccService;
import net.armcloud.paascenter.common.BasicDatabaseEntity;
import net.armcloud.paascenter.common.client.component.CommonPadTaskComponent;
import net.armcloud.paascenter.common.client.internal.dto.*;
import net.armcloud.paascenter.common.client.internal.dto.command.BlackListCMDDTO;
import net.armcloud.paascenter.common.client.internal.dto.command.PadCMDForwardDTO;
import net.armcloud.paascenter.common.client.internal.vo.AddDeviceTaskVO;
import net.armcloud.paascenter.common.client.internal.vo.AddPadTaskVO;
import net.armcloud.paascenter.common.client.internal.vo.VirtualizeDeviceInfoVO;
import net.armcloud.paascenter.common.core.constant.Constants;
import net.armcloud.paascenter.common.core.constant.NumberConsts;
import net.armcloud.paascenter.common.core.constant.comms.CommsConstant;
import net.armcloud.paascenter.common.core.constant.container.TaskTypeEnum;
import net.armcloud.paascenter.common.core.constant.device.PadAllocationStatusConstants;
import net.armcloud.paascenter.common.core.constant.pad.PadConstants;
import net.armcloud.paascenter.common.core.constant.task.PadBackupEnum;
import net.armcloud.paascenter.common.core.constant.task.RedisTaskQueueConstants;
import net.armcloud.paascenter.common.core.constant.task.TaskStatusConstants;
import net.armcloud.paascenter.common.core.constant.task.TaskTypeConstants;
import net.armcloud.paascenter.common.core.exception.BasicException;
import net.armcloud.paascenter.common.enums.SourceTargetEnum;
import net.armcloud.paascenter.common.model.bo.task.PadTaskBO;
import net.armcloud.paascenter.common.model.bo.task.quque.PadUpgradeImageTaskQueueBO;
import net.armcloud.paascenter.common.model.bo.task.quque.UpdatePadAndroidPropTaskQueueBO;
import net.armcloud.paascenter.common.model.dto.api.*;
import net.armcloud.paascenter.common.model.entity.container.TaskRelInstanceDetail;
import net.armcloud.paascenter.common.model.entity.container.TaskRelInstanceDetailImageSucc;
import net.armcloud.paascenter.common.model.entity.file.FileCustomer;
import net.armcloud.paascenter.common.model.entity.filecenter.FileUploadTask;
import net.armcloud.paascenter.common.model.entity.paas.ArmServer;
import net.armcloud.paascenter.common.model.entity.paas.Device;
import net.armcloud.paascenter.common.model.entity.paas.Pad;
import net.armcloud.paascenter.common.model.entity.paas.PadStatus;
import net.armcloud.paascenter.common.model.entity.task.*;
import net.armcloud.paascenter.common.model.vo.api.*;
import net.armcloud.paascenter.common.model.vo.job.SpeedLimitVO;
import net.armcloud.paascenter.common.model.vo.task.PadTaskCallbackVO;
import net.armcloud.paascenter.common.model.vo.task.UpdatePadResetAndRestartVO;
import net.armcloud.paascenter.common.redis.contstant.RedisKeyPrefix;
import net.armcloud.paascenter.common.redis.lock.RedissonDistributedLock;
import net.armcloud.paascenter.common.redis.service.RedisService;
import net.armcloud.paascenter.common.rocketmq.support.DefaultRocketMqProducerWrapper;
import net.armcloud.paascenter.common.redis.delay.RedisDelayTaskUtils;
import net.armcloud.paascenter.common.utils.MD5Utils;
import net.armcloud.paascenter.common.utils.TraceIdHelper;
import net.armcloud.paascenter.commscenter.model.bo.CommsMessageBodyBO;
import net.armcloud.paascenter.filecenter.mapper.FileUploadTaskMapper;
import net.armcloud.paascenter.openapi.constants.ClusterAndNetConstant;
import net.armcloud.paascenter.openapi.controller.internal.PadInternalController;
import net.armcloud.paascenter.openapi.exception.code.PadExceptionCode;
import net.armcloud.paascenter.openapi.helper.NetStoragePadHelper;
import net.armcloud.paascenter.openapi.mapper.ArmServerMapper;
import net.armcloud.paascenter.openapi.mapper.DeviceMapper;
import net.armcloud.paascenter.openapi.mapper.PadMapper;
import net.armcloud.paascenter.openapi.model.dto.PadDetailsDTO;
import net.armcloud.paascenter.openapi.model.vo.PadAndDeviceInfoVO;
import net.armcloud.paascenter.openapi.model.vo.PadDetailsVO;
import net.armcloud.paascenter.openapi.netpadv2.service.NetPadV2Service;
import net.armcloud.paascenter.openapi.service.IDeviceService;
import net.armcloud.paascenter.openapi.service.INetStorageResOffLogService;
import net.armcloud.paascenter.openapi.service.IPadService;
import net.armcloud.paascenter.openapi.service.IPadStatusService;
import net.armcloud.paascenter.task.enums.TaskChannelEnum;
import net.armcloud.paascenter.task.enums.TaskTypeAndChannelEnum;
import net.armcloud.paascenter.task.manager.MessageNotifyManager;
import net.armcloud.paascenter.task.manager.TaskFileManager;
import net.armcloud.paascenter.task.manager.TaskQueueManager;
import net.armcloud.paascenter.task.manager.TaskTaskManager;
import net.armcloud.paascenter.task.mapper.*;
import net.armcloud.paascenter.task.model.dto.PullEdgeClusterConfigurationDTO;
import net.armcloud.paascenter.task.model.dto.PullTaskHealthDTO;
import net.armcloud.paascenter.task.model.dto.PullTaskResultDTO;
import net.armcloud.paascenter.task.model.vo.PullTaskVO;
import net.armcloud.paascenter.task.service.IPullTaskService;
import net.armcloud.paascenter.task.service.ITaskService;
import net.armcloud.paascenter.task.utils.IdGenerateUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.core.task.TaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.hutool.core.bean.BeanUtil.isEmpty;
import static java.util.stream.Collectors.toList;
import static net.armcloud.paascenter.common.core.constant.NumberConsts.ONE;
import static net.armcloud.paascenter.common.core.constant.NumberConsts.ZERO;
import static net.armcloud.paascenter.common.core.constant.device.DeviceStatusConstants.*;
import static net.armcloud.paascenter.common.core.constant.pad.PadStatusConstant.*;
import static net.armcloud.paascenter.common.core.constant.task.TaskStatusConstants.*;
import static net.armcloud.paascenter.common.core.constant.task.TaskTypeConstants.*;
import static net.armcloud.paascenter.common.core.exception.code.BasicExceptionCode.PROCESSING_FAILED;
import static net.armcloud.paascenter.task.utils.IdGenerateUtils.generationSubTaskUniqueId;
import static org.apache.commons.lang3.ObjectUtils.isNotEmpty;

@RefreshScope
@Slf4j
@Service
public class TaskService extends ServiceImpl<TaskMapper, Task> implements ITaskService {
    private final TaskMapper taskMapper;
    private final TaskFileManager taskFileManager;
    private final TaskTaskManager taskTaskManager;

    private final PadTaskMapper padTaskMapper;
    private final FileUploadTaskMapper fileUploadTaskMapper;
    private final CommsCenterManager commsCenterManager;
    private final MessageNotifyManager messageNotifyManager;
    private final TaskTimeoutConfigMapper taskTimeoutConfigMapper;
    private final CustomerTaskIdMapper customerTaskIdMapper;
    @Resource
    private IPadService padService;
    @Resource
    private IPadStatusService padStatusService;
    @Resource
    private DeviceTaskMapper deviceTaskMapper;
    @Resource
    private IDeviceService deviceService;
    @Resource
    private RedissonDistributedLock redissonDistributedLock;
    private final TaskQueueManager taskQueueManager;

    private final TaskQueueMapper taskQueueMapper;
    @Resource
    private TransactionTemplate newTransactionTemplate;
    @Value("${has.open.task-dup:true}")
    private Boolean hasOpenTaskDup;
    @Resource
    private RedisService redisService;
    private final PadBackupTaskInfoMapper padBackupTaskInfoMapper;
    private final PadRestoreTaskInfoMapper padRestoreTaskInfoMapper;

    @Resource
    private TaskExecutor padTaskExecutor;

    @Resource
    private NetStoragePadHelper netStoragePadHelper;

    @Resource
    private CommonPadTaskComponent padTaskComponent;



    @Resource
    private PadMapper padMapper;
    @Resource
    private DeviceMapper deviceMapper;
    @Resource
    private TaskRelInstanceDetailMapper taskRelInstanceDetailMapper;

    @Resource
    private InstanceDetailImageSuccService instanceDetailImageSuccService;
    @Resource
    private IPullTaskService pullTaskService;
    @Resource
    private ArmServerMapper armServerMapper;

    @Resource
    PadInternalController padInternalController;



    private static final List<Integer>  UPDATE_PAD_TASK_BY_WS_CONNECTED_TYPE= Lists.newArrayList(RESET.getType(), RESTART.getType(), UPDATE_PAD_ANDROID_PROP.getType(), REPLACE_PAD.getType(), UPGRADE_IMAGE.getType(),CONTAINER_NET_STORAGE_ON.getType(),NET_PAD_ON.getType(),VIRTUAL_REAL_SWITCH_UPGRADE_IMAGE.getType(),REAL_VIRTUAL_SWITCH_UPGRADE_IMAGE.getType());

    /**禁止并行的任务类型*/
    private static final List<Integer>  PROHIBIT_PARALLEL_TASK_TYPE = Lists.newArrayList(RESET.getType(), RESTART.getType(), UPGRADE_IMAGE.getType(),
            VIRTUAL_REAL_SWITCH_UPGRADE_IMAGE.getType(), REAL_VIRTUAL_SWITCH_UPGRADE_IMAGE.getType(), REPLACE_PAD.getType());

    private static final String SYNCHRONOUS_TASK_CBS_INFO = "2.0.73";
    @Autowired
    private NetPadV2Service netPadV2Service;
    @Autowired
    private DefaultRocketMqProducerWrapper defaultRocketMqProducerWrapper;

    @Override
    public Integer getCustomerTaskId(Long customerId) {
        RLock lock = null;
        try {
            String lockKey = RedisKeyPrefix.CUSTOMER_TASK_ID_KEY + customerId;
            lock = redissonDistributedLock.mustLocked(lockKey);
            customerTaskIdMapper.updateByCustomerId(customerId);
            return customerTaskIdMapper.selectTaskIdByCustomerId(customerId);
        } finally {
            if (lock != null) {
                redissonDistributedLock.unlock(lock);
            }
        }
    }

    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public Integer getMultipleCustomerTaskId(Long customerId, int num) {
        RLock lock = null;
        try {
            String lockKey = RedisKeyPrefix.CUSTOMER_TASK_ID_KEY + customerId;
            lock = redissonDistributedLock.mustLocked(lockKey);
            customerTaskIdMapper.updateByCustomerId(customerId);
            Integer startCustomerTaskId = customerTaskIdMapper.selectTaskIdByCustomerId(customerId);
            int index = num - ONE;
            if (index > ZERO) {
                customerTaskIdMapper.updateMultipleByCustomerId(customerId, index);
            }
            return startCustomerTaskId;
        } finally {
            if (lock != null) {
                redissonDistributedLock.unlock(lock);
            }
        }
    }


    @Override
    public TaskDetailsVO taskDetailsService(TaskDetailsDTO dto) {
        Task masterTask = taskMapper.getByUniqueIdAndCustomerId(dto.getMasterTaskUniqueId(), dto.getCustomerId());
        if (ObjectUtil.isNull(masterTask)) {
            return null;
        }

        TaskDetailsVO taskDetailsVO = new TaskDetailsVO<>();
        taskDetailsVO.setMasterTaskUniqueId(masterTask.getUniqueId());
        taskDetailsVO.setStatus(masterTask.getStatus());
        taskDetailsVO.setType(masterTask.getType());
        taskDetailsVO.setCreateTime(masterTask.getCreateTime().getTime());

        long masterTaskId = masterTask.getId();
        List<String> subTaskUniqueIds = dto.getSubTaskUniqueIds();
        if (masterTask.belongsToPadTask()) {
            List<PadTaskViewVO> padTasks = padTaskMapper.listVOByMasterTaskIdAndUniqueIds(masterTaskId, subTaskUniqueIds);
            taskDetailsVO.setSubTasks(padTasks);
            return taskDetailsVO;
        }

        if (masterTask.belongsToFileTask()) {
            List<FileUploadTask> fileTasks = fileUploadTaskMapper.listByMasterTaskIdAndUniqueIds(masterTaskId, subTaskUniqueIds);
            if (CollectionUtils.isEmpty(fileTasks)) {
                return taskDetailsVO;
            }

            List<Long> customerFileIds = fileTasks.stream().map(FileUploadTask::getFileId).collect(toList());
            List<FileCustomer> fileCustomers = taskFileManager.listCustomerFiles(customerFileIds);
            Map<Long, FileCustomer> customerFileMap = fileCustomers.stream().collect(Collectors.toMap(FileCustomer::getId, fileDetail -> fileDetail, (o1, o2) -> o1));

            List<FileTaskVO> fileTaskVOS = new ArrayList<>(fileTasks.size());
            fileTasks.forEach(fileTask -> {
                FileCustomer fileCustomer = customerFileMap.get(fileTask.getFileId());
                FileTaskVO fileTaskVO = new FileTaskVO();
                fileTaskVO.setSubTaskUniqueId(fileTask.getUniqueId());
                fileTaskVO.setFileUniqueId(fileCustomer.getUniqueId());
                fileTaskVO.setAppId(fileCustomer.getAppId());
                fileTaskVO.setStatus(fileTask.getStatus());
                fileTaskVO.setEndTime(Optional.ofNullable(fileTask.getEndTime()).map(Date::getTime).orElse(null));
                fileTaskVOS.add(fileTaskVO);
            });

            taskDetailsVO.setSubTasks(fileTaskVOS);
            return taskDetailsVO;
        }

        return null;
    }


    @Override
    public List<PadTaskViewVO> padTaskDetailsService(TaskDetailsInfoDTO taskDetailsDTO) {
        return padTaskMapper.listVOByCustomerTaskIdsAndCustomerId(taskDetailsDTO);
    }

    @Override
    public List<FileTaskViewVO> fileTaskDetailsService(TaskDetailsInfoDTO dto) {
        List<FileTaskInfoVo> fileTasks = fileUploadTaskMapper.listVOByCustomerTaskIdsAndCustomerId(dto);
        if (CollectionUtils.isEmpty(fileTasks)) {
            return null;
        }

        List<Long> customerFileIds = fileTasks.stream().map(FileTaskInfoVo::getCustomerFileId).collect(toList());
        List<FileCustomer> fileCustomers = taskFileManager.listCustomerFiles(customerFileIds);
        Map<Long, FileCustomer> customerFileMap = fileCustomers.stream().collect(Collectors.toMap(FileCustomer::getId, fileDetail -> fileDetail, (o1, o2) -> o1));

        List<FileTaskViewVO> fileTaskList = new ArrayList<>(fileTasks.size());
        fileTasks.forEach(fileTask -> {
            FileCustomer fileCustomer = customerFileMap.get(fileTask.getCustomerFileId());
            FileTaskViewVO fileTaskVO = new FileTaskViewVO();
            BeanUtils.copyProperties(fileTask, fileTaskVO);
            fileTaskVO.setFileUniqueId(fileCustomer.getUniqueId());
            fileTaskVO.setFileName(fileCustomer.getFileName());
            fileTaskVO.setAppId(fileCustomer.getAppId());
            fileTaskList.add(fileTaskVO);
        });
        return fileTaskList;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Task updateContainerDeviceTaskResult(ContainerTaskResultVO dto) {
        DeviceTask deviceTask = null;
        if(dto.getPullMode() != null && dto.getPullMode()){
            deviceTask = deviceTaskMapper.selectOne(new QueryWrapper<DeviceTask>().lambda().eq(DeviceTask::getId, dto.getMasterTaskId()).eq(DeviceTask::getDeleteFlag, ZERO).last("LIMIT 1"));
        }else{
            deviceTask = deviceTaskMapper.selectOne(new QueryWrapper<DeviceTask>().lambda().eq(DeviceTask::getContainerTaskId, dto.getMasterTaskId()).eq(DeviceTask::getDeleteFlag, ZERO).last("LIMIT 1"));
        }

        if(isEmpty(deviceTask)){
            log.error("===========> updateVirtualizeDeviceTaskResult ContainerTaskId={} task is null.", dto.getMasterTaskId());
            return null;
        }
        List<Integer> noUpdateStatusList = Arrays.asList(TaskStatusConstants.SUCCESS.getStatus(), TaskStatusConstants.FAIL_ALL.getStatus(), TaskStatusConstants.TIMEOUT.getStatus(), TaskStatusConstants.EXCEPTION.getStatus());
        if (noUpdateStatusList.contains(deviceTask.getStatus())) {
            log.warn("===========> updateVirtualizeDeviceTaskResult ContainerTaskId={} task status is success or failure or timeout, no update.", dto.getMasterTaskId());
            return null;
        }
        DeviceTask par = new DeviceTask();
        par.setStatus(dto.getMasterTaskStatus());
        par.setErrorMsg(dto.getMsg());
        par.setUpdateBy("updateVirtualize");
        if (!TaskStatusConstants.EXECUTING.getStatus().equals(dto.getMasterTaskStatus()) && !TaskStatusConstants.WAIT_EXECUTE.getStatus().equals(dto.getMasterTaskStatus())) {
            par.setEndTime(new Date());
        }
        par.setUpdateTime(new Date());
        int update = deviceTaskMapper.update(par, new QueryWrapper<DeviceTask>().lambda().eq(DeviceTask::getId, deviceTask.getId()).in(DeviceTask::getStatus, TaskStatusConstants.WAIT_EXECUTE.getStatus(), TaskStatusConstants.EXECUTING.getStatus()));
        if (update > ZERO) {
            refreshDeviceMainTask(deviceTask.getTaskId());
            return taskMapper.getById(deviceTask.getTaskId());
        }
        return null;
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public Task updatePadTaskCallback(ContainerTaskResultVO dto) {
        LambdaUpdateWrapper<PadTask> queryWrapper = new LambdaUpdateWrapper<>();
        queryWrapper.eq(PadTask::getContainerTaskId,dto.getMasterTaskId()).last("LIMIT 1");
        PadTask dbPadTask = padTaskMapper.selectOne(queryWrapper);
        //找不到任务
        if(Objects.isNull(dbPadTask)){
            log.error("updatePadTaskCallback selectList task is null. ContainerTaskResultVO:{}",JSON.toJSONString(dto));
        }

        PadTask padTask = new PadTask();
        padTask.setContainerTaskId(dto.getMasterTaskId());
        padTask.setStatus(dto.getMasterTaskStatus());
        padTask.setErrorMsg(dto.getMsg());
        //不是开始执行跟执行中,打上结束时间
        if (!Objects.equals(dto.getMasterTaskStatus(), EXECUTING.getStatus()) && !Objects.equals(dto.getMasterTaskStatus(), WAIT_EXECUTE.getStatus())) {
            padTask.setEndTime(new Date());
        }
        LambdaUpdateWrapper<PadTask> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(PadTask::getContainerTaskId,dto.getMasterTaskId());
        //修改子任务状态
        padTaskMapper.update(padTask,wrapper);
        //刷新主任务状态
        taskTaskManager.refreshMasterTaskStatus(dbPadTask.getTaskId());



        return null;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Task updatePadTaskCallbackTypeOn(Task task,ContainerTaskResultVO dto) {
        LambdaUpdateWrapper<PadTask> queryWrapper = new LambdaUpdateWrapper<>();
        queryWrapper.eq(PadTask::getId,dto.getMasterTaskId()).last("LIMIT 1");
        PadTask dbPadTask = padTaskMapper.selectOne(queryWrapper);
        //找不到任务
        if(Objects.isNull(dbPadTask)){
            log.error("updatePadTaskCallback selectList task is null. ContainerTaskResultVO:{}",JSON.toJSONString(dto));
        }

        PadTask padTask = new PadTask();
//        padTask.setContainerTaskId(dto.getMasterTaskId());
        padTask.setStatus(dto.getMasterTaskStatus());
        padTask.setErrorMsg(dto.getMsg());
        //不是开始执行跟执行中,打上结束时间
        if (!Objects.equals(dto.getMasterTaskStatus(), EXECUTING.getStatus()) && !Objects.equals(dto.getMasterTaskStatus(), WAIT_EXECUTE.getStatus())) {
            padTask.setEndTime(new Date());
        }
        LambdaUpdateWrapper<PadTask> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(PadTask::getId,dto.getMasterTaskId());
        //修改子任务状态
        padTaskMapper.update(padTask,wrapper);
        //刷新主任务状态
        taskTaskManager.refreshMasterTaskStatus(dbPadTask.getTaskId());
        dbPadTask.setStatus(dto.getMasterTaskStatus());
        //发送任务状态变更
        messageNotifyManager.sendPadTaskStatusChangeMessage(task, dbPadTask);

        dbPadTask.setStatus(dto.getMasterTaskStatus());
        //发送任务状态变更
        messageNotifyManager.sendPadTaskStatusChangeMessage(task, dbPadTask);

        return null;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean updateDeviceTaskResult(List<AddDeviceTaskVO> deviceTasks) {
        for (AddDeviceTaskVO vo : deviceTasks) {
            DeviceTask deviceTask = new DeviceTask();
            deviceTask.setId(vo.getSubTaskId());
            deviceTask.setStatus(vo.getSubTaskStatus());
            deviceTask.setErrorMsg(vo.getErrorMsg());
            deviceTask.setEndTime(new Date());
            deviceTask.setContainerTaskId(vo.getContainerTaskId());
            deviceTask.setUpdateBy("updateDevice");
            deviceTask.setUpdateTime(new Date());
            deviceTask.setTaskId(vo.getMasterTaskId());
            if (Objects.nonNull(vo.getContainerTaskId()) && WAIT_EXECUTE.getStatus().equals(vo.getSubTaskStatus())) {
                deviceTaskMapper.updateSetTimeout(deviceTask);
            } else {
                deviceTaskMapper.updateById(deviceTask);
            }
        }
        //刷新主任务状态
        refreshDeviceMainTask(deviceTasks.get(ZERO).getMasterTaskId());
        return true;
    }

    private void refreshDeviceMainTask(Long masterTaskId) {
        List<DeviceTask> subTaskList = deviceTaskMapper.selectList(new QueryWrapper<DeviceTask>().eq("task_id", masterTaskId).eq("delete_flag", ZERO));
        long total = subTaskList.size();
        long successCount = subTaskList.stream().filter(dto -> Objects.equals(dto.getStatus(), SUCCESS.getStatus())).count();
        List<Integer> failStatusList = Arrays.asList(FAIL_ALL.getStatus(), TaskStatusConstants.TIMEOUT.getStatus());
        long failCount = subTaskList.stream().filter(dto -> failStatusList.contains(dto.getStatus())).count();
        Task updateTask = new Task();
        updateTask.setId(masterTaskId);
        if (successCount == total) {
            updateTask.setStatus(SUCCESS.getStatus());
        }

        if (failCount > 0) {
            updateTask.setStatus(TaskStatusConstants.FAIL_PART.getStatus());
        }

        if (failCount == total) {
            updateTask.setStatus(FAIL_ALL.getStatus());
        }

        if (isNotEmpty(updateTask.getStatus())) {
            taskMapper.updateById(updateTask);
        }
    }

    @Override
    public List<PadTask> selectTaskByTaskTypeAndTaskStatus(List<Pad> pads, List<Integer> status) {
        for (Pad pad : pads) {
            return padTaskMapper.selectList(new QueryWrapper<PadTask>().eq("pad_code", pad.getPadCode()).in("status", status).eq("delete_flag", ZERO));
        }
        return null;
    }

    // 将需要处理的任务类型放入一个 List
    private static final List<Integer> allowedTaskTypes = Arrays.asList(
            UPGRADE_IMAGE.getType(),
            LIMIT_BANDWIDTH.getType(),
            UPDATE_PAD_ANDROID_PROP.getType(),
            MODIFY_PROPERTIES_PAD.getType(),
            REPLACE_PAD.getType(),
            BACKUP_PAD.getType(),
            RESTORE_PAD.getType(),
            VIRTUAL_REAL_SWITCH_UPGRADE_IMAGE.getType(),
            REPLACE_REAL_ADB.getType(),
            REAL_VIRTUAL_SWITCH_UPGRADE_IMAGE.getType(),
            CONTAINER_NET_STORAGE_OFF.getType(),
            CONTAINER_NET_STORAGE_DELETE.getType(),
            CONTAINER_NET_STORAGE_BACKUP.getType(),
            CONTAINER_NET_STORAGE_RES_UNIT_DELETE.getType(),
            FORCE_POWER_OFF.getType(),
            NET_SYNC_BACKUP.getType(),
            NET_PAD_ON.getType(),
            NET_PAD_OFF.getType(),
            NET_PAD_DEL.getType()
    );

    @Override
    public Boolean updateContainerInstanceTaskResult(ContainerInstanceTaskResultDTO dto) {
        PadTask padTask;
        if(dto.getPullMode() != null && dto.getPullMode()){
            padTask = padTaskMapper.selectOne(new QueryWrapper<PadTask>().lambda().eq(PadTask::getId, dto.getMasterTaskId()).eq(PadTask::getDeleteFlag, ZERO).in(PadTask::getStatus, TaskStatusConstants.WAIT_EXECUTE.getStatus(), TaskStatusConstants.EXECUTING.getStatus()).last("LIMIT 1"));
        }else{
            padTask = padTaskMapper.selectOne(new QueryWrapper<PadTask>().lambda().eq(PadTask::getContainerTaskId, dto.getMasterTaskId()).eq(PadTask::getDeleteFlag, ZERO).in(PadTask::getStatus, TaskStatusConstants.WAIT_EXECUTE.getStatus(), TaskStatusConstants.EXECUTING.getStatus()).last("LIMIT 1"));
        }
        log.info("updateContainerInstanceTaskResult_debug info: padTask={}", JSON.toJSONString(padTask));
        if (Objects.nonNull(padTask)) {
            List<Integer> noUpdateStatusList = Arrays.asList(TaskStatusConstants.SUCCESS.getStatus(), TaskStatusConstants.FAIL_ALL.getStatus(), TaskStatusConstants.TIMEOUT.getStatus(), TaskStatusConstants.EXCEPTION.getStatus());

            long taskId = padTask.getTaskId();
            Task task = taskMapper.getById(taskId);

            if (noUpdateStatusList.contains(padTask.getStatus()) && !Objects.equals(NET_SYNC_BACKUP.getType(), task.getType())) {
                log.warn("===========> updateContainerInstanceTaskResult ContainerTaskId={} task status is success or failure or timeout, no update.", dto.getMasterTaskId());
                return null;
            }

            // 如果任务类型不在 List 中则返回 false
            // 如果任务明确失败了 则由这里处理
            if (!allowedTaskTypes.contains(task.getType()) && !Objects.equals(dto.getMasterTaskStatus(), TaskStatusConstants.FAIL_ALL.getStatus())) {
                return false;
            }

            // 网存2.0 开机任务成功后，修改实例状态为运行中。任务不做处理，等健康上报（与之前逻辑相同。）
            if (Objects.equals(NET_PAD_ON.getType(), task.getType()) && Objects.equals(dto.getMasterTaskStatus(), SUCCESS.getStatus())) {
                // 不推送回调
                return false;
            }
            if (Objects.equals(NET_PAD_ON.getType(), task.getType()) && Objects.equals(dto.getMasterTaskStatus(), FAIL_ALL.getStatus())) {
                // 网存2.0开机失败 按关机处理
                netPadV2Service.padOffHandler(padTask.getPadCode());
            }
            if (Objects.equals(NET_PAD_DEL.getType(), task.getType())) {
                if (Objects.equals(dto.getMasterTaskStatus(), SUCCESS.getStatus())) {
                    // 删除成功，进行后续处理
                    netPadV2Service.padDelHandler(padTask.getPadCode());
                } else if (Objects.equals(dto.getMasterTaskStatus(), FAIL_ALL.getStatus())) {
                    // 更新实例状态为删除失败
                    padStatusService.updatePadStatusAndSendPadStatusCallback(Collections.singletonList(padTask.getPadCode()), DELETE_FAIL, padTask.getCustomerId(), "updateContainerInstanceTaskResult");
                }
            }
//            Optional.ofNullable(dto.getMsg())
//                    .filter(msg -> msg.contains("连接云机失败"))
//                    .ifPresent(msg -> {
//                        // 处理逻辑
//                        DingTalkRobotClient.sendMessage(springProfilesActive,msg);
//                    });

            //处理修改实例成功回调
            if (Objects.equals(MODIFY_PROPERTIES_PAD.getType(), task.getType()) && Objects.equals(dto.getMasterTaskStatus(), SUCCESS.getStatus())) {
                processModifyPropertiesPadCallBack(padTask);
            }

            log.info("updateContainerInstanceTaskResult_debug info:开始处理克隆内存 padTask={}", JSON.toJSONString(padTask));


            //网存关机执行成功,或者失败
            if (Objects.equals(CONTAINER_NET_STORAGE_OFF.getType(), task.getType())
                    && !(Objects.equals(dto.getMasterTaskStatus(), TaskStatusConstants.WAIT_EXECUTE.getStatus())
                    || Objects.equals(dto.getMasterTaskStatus(), TaskStatusConstants.EXECUTING.getStatus()))) {
                //未找到实例,所以实例已经不在板卡了,也处理为成功
                boolean isInstanceNotFound = dto.getMsg() != null && dto.getMsg().contains("未找到此实例");
                boolean isTaskSuccess = Objects.equals(dto.getMasterTaskStatus(), SUCCESS.getStatus());

                PadStatusDTO padStatusDTO = new PadStatusDTO();
                padStatusDTO.setPadCode(dto.getPadCode());
                padStatusDTO.setOprBusiness("taskQueue-updateTaskResult");
                if (isInstanceNotFound || isTaskSuccess) {
                    // 添加CBS版本检查限制，如果CBS版本低于2.0.73以下的直接释放算力，不需要下发同步任务
                    String deviceCbsInfo = deviceMapper.selectDeviceCbsInfoByPadCode(dto.getPadCode());
                    if(StringUtils.isNotBlank(deviceCbsInfo)){
                        int cmp = compareVersions(deviceCbsInfo.trim(), SYNCHRONOUS_TASK_CBS_INFO);
                        if (cmp < 0) {
                            // 版本 < 2.0.73：直接释放算力
                            padInternalController.unbindTheCardInformation(padStatusDTO);
                            log.info("padCode={}, CBS 版本 {} < 2.0.73，已释放算力", dto.getPadCode(), deviceCbsInfo);
                        }else{
                            try {
                                // 创建同步备份任务
                                PadDetailsDTO padDetailsDTO = new PadDetailsDTO();
                                padDetailsDTO.setPadCodes(Lists.newArrayList(dto.getPadCode()));
                                List<PadDetailsVO> detailsVOList = padMapper.selectDetailsByPadCode(padDetailsDTO);
                                NetPadSyncDTO netPadSyncDTO = BeanUtil.copyProperties(detailsVOList.get(0),NetPadSyncDTO.class);
                                padTaskComponent.addReplacePadTask(padTask.getCustomerId(),
                                        Lists.newArrayList(dto.getPadCode()),
                                        TaskTypeConstants.NET_SYNC_BACKUP, null,
                                        JSONObject.toJSONString(netPadSyncDTO),
                                        SourceTargetEnum.PAAS);
                                log.info("NET_SYNC_BACKUP task created for padCode={}", dto.getPadCode());
                            } catch (Exception e) {
                                log.error("Failed to create NET_SYNC_BACKUP task for padCode={}, error: {}", dto.getPadCode(), e.getMessage(), e);
                                padStatusDTO.setPadStatus(OFF_ERROR);
                                dto.setMasterTaskStatus(-1);
                                dto.setMsg("数据备份失败，请稍后重试");
                            }
                            netStoragePadHelper.onlyNetStoragePadOff(dto.getPadCode());
                        }
                    }else{
                        log.warn("updateContainerInstanceTaskResult device is null,task:{}",JSONUtil.toJsonStr(task));
                    }

                }else {
                    padStatusDTO.setPadStatus(OFF_ERROR); // 关机失败,状态打成异常,只修改状态
                }
                padInternalController.updatePadStatus(padStatusDTO);
            }

            if (Objects.equals(NET_PAD_OFF.getType(), task.getType())
                    && !(Objects.equals(dto.getMasterTaskStatus(), TaskStatusConstants.WAIT_EXECUTE.getStatus())
                    || Objects.equals(dto.getMasterTaskStatus(), TaskStatusConstants.EXECUTING.getStatus()))) {
                if (Objects.equals(dto.getMasterTaskStatus(), SUCCESS.getStatus())) {
                    // 关机成功，处理关机后的逻辑。
                    netPadV2Service.padOffHandler(padTask.getPadCode());
                }
                if (Objects.equals(dto.getMasterTaskStatus(), FAIL_ALL.getStatus())) {
                    // 关机失败，修改实例状态为关机失败
                    PadStatusDTO padStatusDTO = new PadStatusDTO();
                    padStatusDTO.setPadCode(dto.getPadCode());
                    padStatusDTO.setPadStatus(OFF_ERROR);
                    padStatusDTO.setOprBusiness("taskQueue-updateTaskResult");
                    padInternalController.updatePadStatus(padStatusDTO);
                }
            }
            //网存回收资源成功
            if (Objects.equals(CONTAINER_NET_STORAGE_DELETE.getType(), task.getType())) {
                if (Objects.equals(dto.getMasterTaskStatus(), SUCCESS.getStatus())) {
                    PadStatusDTO padStatusDTO = new PadStatusDTO();
                    padStatusDTO.setPadCode(dto.getPadCode());
                    //关机成功
                    padStatusDTO.setPadStatus(OFF);
                    padStatusDTO.setOprBusiness("taskQueue-updateTaskResult");
                    padInternalController.deletePadInformation(padStatusDTO);
                } else if (Objects.equals(dto.getMasterTaskStatus(), FAIL_ALL.getStatus())) {
                    // 更新实例状态为删除失败
                    padStatusService.updatePadStatusAndSendPadStatusCallback(Collections.singletonList(padTask.getPadCode()), DELETE_FAIL, padTask.getCustomerId(), "updateContainerInstanceTaskResult");
                }
            }
            //升级云真机adb模板,一键新机修改adi模板,需要修改屏幕布局信息，另外需要关联对应的云真机模板id
            if ((Objects.equals(REPLACE_REAL_ADB.getType(), task.getType()) ||Objects.equals(REPLACE_PAD.getType(), task.getType())) && Objects.equals(dto.getMasterTaskStatus(), SUCCESS.getStatus())) {
                processReplaceRealAdiPadCallBack(padTask);
            }
            //完成指令后 需要重启实例的指令 应该gameserver连接上实例后再更新任务状态为完成 否则使用方可能因实例还未连接成功导致一些业务异常
            //1023指令由于可以由客户设置是否重启 如果设置了不重启 这里就应该要处理结果 否则不用处理 callback会处理
            if(UPDATE_PAD_ANDROID_PROP.getType().equals(task.getType()) && StrUtil.isNotEmpty(padTask.getTaskContent())){
                UpdatePadAndroidPropTaskQueueBO updatePadAndroidPropTaskQueueBO = JSON.parseObject(padTask.getTaskContent(),UpdatePadAndroidPropTaskQueueBO.class);
                if(updatePadAndroidPropTaskQueueBO != null && updatePadAndroidPropTaskQueueBO.getRestart()){
                    return false;
                }
            }
            //一键新机不处理任务结果
            if(Objects.equals(REPLACE_PAD.getType(), task.getType())){
                if (Objects.equals(dto.getMasterTaskStatus(), FAIL_ALL.getStatus())) {
                    //实例状态变更，发送回调消息
                    padStatusService.updatePadStatusAndSendPadStatusCallback(Collections.singletonList(padTask.getPadCode()), RUNNING, padTask.getCustomerId(), "replacePadFailResult");
                    this.updatePadTaskStatusByContainerTaskId(taskId, dto.getMasterTaskStatus(), dto.getMsg());
                    this.updateTaskStatusByContainerTaskId(taskId, dto.getMasterTaskStatus());
                }
                return true;
            }

            // 强制关机成功，释放算力，修改实例状态为已关机
            if (Objects.equals(FORCE_POWER_OFF.getType(), task.getType())) {
                forcePowerOffResultHandler(dto);
            }

            // PaaS实例任务同步cms容器任务状态
            boolean updateFlag = changeTaskStatusService(dto, task, padTask);
            //开机失败
            if (Objects.equals(CONTAINER_NET_STORAGE_ON.getType(), task.getType())
                    && !Lists.newArrayList(WAIT_EXECUTE.getStatus(), EXECUTING.getStatus(), SUCCESS.getStatus()).contains(dto.getMasterTaskStatus())) {
                PadStatusDTO padStatusDTO = new PadStatusDTO();
                padStatusDTO.setPadCode(StringUtils.isBlank(dto.getPadCode()) ? padTask.getPadCode() : dto.getPadCode());
                padStatusDTO.setPadStatus(ON_ERROR);
                padStatusDTO.setOprBusiness("taskQueue-updateTaskResult");
                log.info("updateContainerInstanceTaskResult_CONTAINER_NET_STORAGE_ON debug info:开机失败,padStatusDTO: {}", JSONObject.toJSONString(padStatusDTO));
                padInternalController.updatePadStatus(padStatusDTO);
                // 网存开机失败需要释放算力
                padInternalController.unbindTheCardInformation(padStatusDTO);
            }

            //处理克隆内存失败
            if (Objects.equals(CONTAINER_NET_STORAGE_BACKUP.getType(), task.getType()) && !Arrays.asList(WAIT_EXECUTE.getStatus(), EXECUTING.getStatus(), SUCCESS.getStatus()).contains(dto.getMasterTaskStatus())) {
                log.info("updateContainerInstanceTaskResult_debug info:处理克隆内存失败 padTask={}", JSON.toJSONString(padTask));
                processNetStorageBackupCallback(padTask);
            }
            if (Objects.equals(CONTAINER_NET_STORAGE_RES_UNIT_DELETE.getType(), task.getType()) && Arrays.asList(SUCCESS.getStatus()).contains(dto.getMasterTaskStatus())) {
                log.info("updateContainerInstanceTaskResult_debug info: CONTAINER_NET_STORAGE_RES_UNIT_DELETE padTask={}", JSON.toJSONString(padTask));
                processNetStorageResUnitDeleteCallback(padTask);
            }
            if (Objects.equals(BACKUP_PAD.getType(), task.getType()) && updateFlag) {
                handlerBackupResult(dto, padTask);
            }

            if (Objects.equals(RESTORE_PAD.getType(), task.getType()) && updateFlag) {
                handlerRestoreResult(dto, padTask);
            }

            if (Objects.equals(LIMIT_BANDWIDTH.getType(), task.getType()) && updateFlag) {
                if (TaskStatusConstants.EXECUTING.getStatus().equals(dto.getMasterTaskStatus()) || TaskStatusConstants.WAIT_EXECUTE.getStatus().equals(dto.getMasterTaskStatus())) {
                    return false;
                }

                SpeedLimitVO param = JSONUtil.toBean(padTask.getTaskContent(), SpeedLimitVO.class);
                PadBandwidthDTO padBandwidthDTO = new PadBandwidthDTO(padTask.getPadCode(), param.getUpBandwidth(), param.getDownBandwidth());
                padService.updatePadBandwidthService(padBandwidthDTO);
            }

            //任务成功，升级真机镜像则更新pad的类型
            if (SUCCESS.getStatus().equals(dto.getMasterTaskStatus()) && Objects.equals(VIRTUAL_REAL_SWITCH_UPGRADE_IMAGE.getType(), task.getType()) && updateFlag) {
                PadTypeDTO padTypeDTO = new PadTypeDTO();
                padTypeDTO.setPadCode(padTask.getPadCode());
                padTypeDTO.setType(PadConstants.Type.REAL.getValue());
                padService.updatePadTypeService(padTypeDTO);
                // 关联adi模板
                String taskContent = taskQueueMapper.selectContentJsonOne(padTask.getTaskId(),padTask.getPadCode());
                PadUpgradeImageTaskQueueBO param = JSONUtil.toBean(taskContent, PadUpgradeImageTaskQueueBO.class);
                if(Objects.nonNull(param.getRealPhoneTemplateId())){
                    padService.updatePadRealPhoneTemplate(padTask.getPadCode(),param.getRealPhoneTemplateId());
                }
            }

            //任务成功，真机切换虚拟机镜像则更新pad的类型
            if(SUCCESS.getStatus().equals(dto.getMasterTaskStatus()) && Objects.equals(REAL_VIRTUAL_SWITCH_UPGRADE_IMAGE.getType(), task.getType()) && updateFlag){
                PadTypeDTO padTypeDTO = new PadTypeDTO();
                padTypeDTO.setPadCode(padTask.getPadCode());
                padTypeDTO.setType(PadConstants.Type.VIRTUAL.getValue());
                padService.updatePadTypeService(padTypeDTO);
            }

            if ((Objects.equals(UPGRADE_IMAGE.getType(), task.getType()) || Objects.equals(VIRTUAL_REAL_SWITCH_UPGRADE_IMAGE.getType(), task.getType())
                    || Objects.equals(REAL_VIRTUAL_SWITCH_UPGRADE_IMAGE.getType(), task.getType())) && updateFlag) {
                    if (SUCCESS.getStatus().equals(dto.getMasterTaskStatus())) {
                        //实例状态变更，发送回调消息
                        padStatusService.updatePadStatusAndSendPadStatusCallback(Collections.singletonList(padTask.getPadCode()), RUNNING, padTask.getCustomerId(), "updateContainerInstanceTaskResult");
                    } else if (FAIL_ALL.getStatus().equals(dto.getMasterTaskStatus()) || EXCEPTION.getStatus().equals(dto.getMasterTaskStatus())) {
                        //实例状态变更，发送回调消息
                        padStatusService.updatePadStatusAndSendPadStatusCallback(Collections.singletonList(padTask.getPadCode()), ABNORMAL, padTask.getCustomerId(), "updateContainerInstanceTaskResult");
                    }
                    return true;
                }
            // 网存同步备份任务、同步成功释放算力
            if (Objects.equals(NET_SYNC_BACKUP.getType(), task.getType())) {
                if(Objects.equals(dto.getMasterTaskStatus(), SUCCESS.getStatus())
                        && !Objects.equals(dto.getResult(), "ACK") ){
                    PadStatusDTO padStatusDTO = new PadStatusDTO();
                    padStatusDTO.setPadCode(dto.getPadCode());
                    padStatusDTO.setOprBusiness("taskQueue-updateTaskResult");
                    padInternalController.unbindTheCardInformation(padStatusDTO);
                    log.info("网存同步备份任务成功,释放算力 data:{}",JSONUtil.toJsonStr(padStatusDTO));

                   //修改状态关机完成
                   padStatusService.lambdaUpdate().eq(PadStatus::getPadCode,dto.getPadCode()).set(PadStatus::getPadStatus,OFF).update();

                }else{
                    log.error("网存同步备份任务失败 data:{}",JSONUtil.toJsonStr(dto));
                    if (!Objects.equals(dto.getResult(), "ACK")) {
                        PadStatusDTO padStatusDTO = new PadStatusDTO();
                        padStatusDTO.setPadCode(StringUtils.isBlank(dto.getPadCode()) ? padTask.getPadCode() : dto.getPadCode());
                        padStatusDTO.setPadStatus(OFF_ERROR);
                        padStatusDTO.setOprBusiness("taskQueue-updateTaskResult");
                        log.info("网存同步失败, 更新实例状态为关机失败，padStatusDTO: {}", JSONObject.toJSONString(padStatusDTO));
                        padInternalController.updatePadStatus(padStatusDTO);
                    }
                }
            }
            log.warn("===========> updateContainerInstanceTaskResult ContainerTaskId={} task is null.", dto.getMasterTaskId());
            return false;
        }
        return false;
    }

    private void forcePowerOffResultHandler(ContainerInstanceTaskResultDTO dto) {
        if (Objects.equals(dto.getMasterTaskStatus(), SUCCESS.getStatus())) {
            // 修改实例状态为已关机
            log.info("updateContainerInstanceTaskResult debug info: 强制关机成功，修改实例状态为已关机 padCode={}", dto.getPadCode());
            PadStatusDTO padStatusDTO = new PadStatusDTO();
            padStatusDTO.setPadCode(dto.getPadCode());
            padStatusDTO.setPadStatus(OFF);
            padStatusDTO.setOprBusiness("taskQueue-updateTaskResult");
            padInternalController.updatePadStatus(padStatusDTO);
            // 释放算力
            padInternalController.unbindTheCardInformation(padStatusDTO);
        }
        // 失败
        if (Objects.equals(dto.getMasterTaskStatus(), FAIL_ALL.getStatus())) {
            log.info("updateContainerInstanceTaskResult debug info: 强制关机失败 padCode={}", dto.getPadCode());
            PadStatusDTO padStatusDTO = new PadStatusDTO();
            padStatusDTO.setPadCode(dto.getPadCode());
            padStatusDTO.setPadStatus(OFF_ERROR);
            padStatusDTO.setOprBusiness("taskQueue-updateTaskResult");
            padInternalController.updatePadStatus(padStatusDTO);
        }
    }

    private void processNetStorageBackupCallback(PadTask padTask) {
        String netStorageResUnitCode = padTask.getPadCode();
        netStoragePadHelper.processNetStorageBackupCallback(netStorageResUnitCode);
    }

    /**
     * 处理网存存储删除回调
     * 2025-06-26 网存备份删除不再调用任务
     * @param padTask
     */
    @Deprecated
    private void processNetStorageResUnitDeleteCallback(PadTask padTask) {
        String netStorageResUnitCode = padTask.getPadCode();
        netStoragePadHelper.processNetStorageResUnitDeleteCallback(netStorageResUnitCode);
    }



    public void updatePadTaskStatusByContainerTaskId(Long taskId, Integer status, String msg) {
        log.info("updateStatusByContainerTaskId taskId:{} status:{},msg:{}", taskId, status, msg);
        LambdaUpdateWrapper<PadTask> updateWrapper = new LambdaUpdateWrapper<PadTask>();
        updateWrapper.eq(PadTask::getTaskId, taskId);
        updateWrapper.set(PadTask::getStatus, status);
        updateWrapper.set(PadTask::getErrorMsg, msg);
        updateWrapper.set(PadTask::getUpdateTime, new Date());
        updateWrapper.set(PadTask::getEndTime, new Date());
        padTaskMapper.update(updateWrapper);
    }

    public void updateTaskStatusByContainerTaskId(Long taskId, Integer status) {
        log.info("updateTaskStatusByContainerTaskId taskId:{} status:{}", taskId, status);
        LambdaUpdateWrapper<Task> updateWrapper = new LambdaUpdateWrapper<Task>();
        updateWrapper.eq(Task::getId, taskId);
        updateWrapper.set(Task::getStatus, status);
        updateWrapper.set(Task::getUpdateTime, new Date());
        taskMapper.update(updateWrapper);
    }

    public void updateSpecialPadTaskStatus(String padCode) {
        Long padTaskId = padTaskMapper.selectTheTypeDoingPadTask(padCode, UPGRADE_IMAGE.getType());
        if (padTaskId == null) {
            return;
        }
        log.info("updateSpecialPadTaskStatus padTaskId:{}", padTaskId);
        LambdaUpdateWrapper<PadTask> updateWrapper = new LambdaUpdateWrapper<PadTask>();
        updateWrapper.eq(PadTask::getId, padTaskId);
        updateWrapper.set(PadTask::getStatus, TaskStatusConstants.SUCCESS.getStatus());
        updateWrapper.set(PadTask::getUpdateTime, new Date());
        updateWrapper.set(PadTask::getEndTime, new Date());
        int update = padTaskMapper.update(updateWrapper);

        if (update > 0) {
            PadTask padTask = padTaskMapper.getById(padTaskId);
            Task task = taskMapper.getById(padTask.getTaskId());
            taskTaskManager.refreshMasterTaskStatus(padTask.getTaskId());
            padTask.setStatus(SUCCESS.getStatus());
            padTask.setEndTime(new Date());
            messageNotifyManager.sendPadTaskStatusChangeMessage(task, padTask);
        }

    }

    public void updateSpecialDeviceTaskStatus(String deviceCode) {
        List<Integer> taskTypes = Arrays.asList(CONTAINER_DEVICE_DESTROY.getType());
        List<Long> deviceTaskIds = deviceTaskMapper.selectTheTypeDoingDeviceTask(deviceCode, taskTypes);
        if (CollUtil.isEmpty(deviceTaskIds)) {
            return;
        }
        log.info("updateSpecialDeviceTaskStatus deviceTaskIds:{}", deviceTaskIds);
        for(Long deviceTaskId : deviceTaskIds){
            LambdaUpdateWrapper<DeviceTask> updateWrapper = new LambdaUpdateWrapper<DeviceTask>();
            updateWrapper.eq(DeviceTask::getId, deviceTaskId);
            updateWrapper.set(DeviceTask::getStatus, TaskStatusConstants.SUCCESS.getStatus());
            updateWrapper.set(DeviceTask::getUpdateTime, new Date());
            updateWrapper.set(DeviceTask::getEndTime, new Date());
            int update = deviceTaskMapper.update(updateWrapper);

            if (update > 0) {
                DeviceTask deviceTask = deviceTaskMapper.getById(deviceTaskId);
                taskTaskManager.refreshMasterTaskStatus(deviceTask.getTaskId());
            }

            //更新分配状态 这里后面增加类型要区分 现在只有重置板卡
            Device device = deviceMapper.selectOne(new QueryWrapper<>(Device.class).eq("device_code",deviceCode).eq("delete_flag",0).last("limit 1"));
            if(device != null){
                deviceMapper.updatePadAllocationStatusById(Collections.singletonList(device.getId()),PadAllocationStatusConstants.UNALLOCATED.getStatus());
            }
        }
    }


    /**
     * 处理修改实例回调并处理信息
     *
     * @param padTask
     */
    private void processModifyPropertiesPadCallBack(PadTask padTask) {
        try {
            ModifyPadPropertiesDTO param = JSONUtil.toBean(padTask.getTaskContent(), ModifyPadPropertiesDTO.class);
            log.info("processModifyPropertiesPadCallBack param{}:",param);
            PadLayoutCodeDto dto = PadLayoutCodeDto.buildByModifyPadPropertiesDTO(param, padTask.getPadCode());
            if (Objects.nonNull(dto)) {
                padService.updatePadLayoutCode(dto);
            }if(Objects.nonNull(param.getDns())){
                padService.updatePadDns(padTask.getPadCode(),param.getDns());
            }
        } catch (Exception e) {
            log.error("TaskService.processModifyPropertiesPadCallBack error: ", e);
        }

    }

    /**
     * 升级云真机adb模板,需要修改屏幕布局信息
     * @param padTask
     */
    private void processReplaceRealAdiPadCallBack(PadTask padTask) {
        try {
            String taskContent = taskQueueMapper.selectContentJsonOne(padTask.getTaskId(),padTask.getPadCode());
            PadUpgradeImageTaskQueueBO param = JSONUtil.toBean(taskContent, PadUpgradeImageTaskQueueBO.class);
            PadLayoutCodeDto dto = PadLayoutCodeDto.buildByPadUpgradeImageTaskQueueBO(param, padTask.getPadCode());
            if (Objects.nonNull(dto)) {
                padService.updatePadLayoutCode(dto);
            }
            if(Objects.nonNull(param.getRealPhoneTemplateId())){
                padService.updatePadRealPhoneTemplate(padTask.getPadCode(),param.getRealPhoneTemplateId());
            }
        } catch (Exception e) {
            log.error("TaskService.processModifyPropertiesPadCallBack error: ", e);
        }

    }

    private void handlerRestoreResult(ContainerInstanceTaskResultDTO dto, PadTask padTask) {
        String padCode = dto.getPadCode();
        int cmsTaskStatus = dto.getMasterTaskStatus();
        if (cmsTaskStatus == WAIT_EXECUTE.getStatus()) {
            return;
        }

        if (cmsTaskStatus == EXECUTING.getStatus()) {
            padStatusService.updatePadStatusAndSendPadStatusCallback(Collections.singletonList(padCode), RESTORE, padTask.getCustomerId(), "updateContainerInstanceTaskResult");
            return;
        }
        padStatusService.updatePadStatusAndSendPadStatusCallback(Collections.singletonList(padCode), RUNNING, padTask.getCustomerId(), "updateContainerInstanceTaskResult");
    }

    private void handlerBackupResult(ContainerInstanceTaskResultDTO dto, PadTask padTask) {
        String padCode = dto.getPadCode();
        int cmsTaskStatus = dto.getMasterTaskStatus();
        if (cmsTaskStatus == WAIT_EXECUTE.getStatus()) {
            return;
        }

        String msg = dto.getMsg();
        if (StringUtils.isNotBlank(msg)) {
            try {
                JSONObject jsonObject = JSONObject.parseObject(dto.getMsg());
                Object filesize = jsonObject.get("filesize");
                if ((filesize instanceof Long || filesize instanceof Integer)) {
                    padBackupTaskInfoMapper.updateBackupSizeBySubTaskId(padTask.getId(), Long.parseLong(String.valueOf(filesize)));
                }
            } catch (com.alibaba.fastjson2.JSONException ignored) {
                // 这个异常是尝试从结果解析备份文件大小，序列化异常无需处理
            }
        }

        if (cmsTaskStatus == EXECUTING.getStatus()) {
            padStatusService.updatePadStatusAndSendPadStatusCallback(Collections.singletonList(padCode), BACKUP, padTask.getCustomerId(), "updateContainerInstanceTaskResult");
            return;
        }

        padStatusService.updatePadStatusAndSendPadStatusCallback(Collections.singletonList(padCode), RUNNING, padTask.getCustomerId(), "updateContainerInstanceTaskResult");
    }

    @Override
    public void addDeviceTaskBmcTaskId(List<BmcTaskInfoVO> bmcTaskInfoVOs) {
        bmcTaskInfoVOs.forEach(vo -> {
            //deviceTaskMapper.update(new UpdateWrapper<DeviceTask>().eq("customer_task_id", vo.getOutTaskId()).set("bmc_task_id", vo.getTaskId()));
            deviceTaskMapper.updateDeviceTask(vo.getTaskId(), vo.getOutTaskId(), vo.getTaskType());
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<PadTask> addBackupTask(AddBackupTaskDTO param) {
        List<String> padCodes = param.getPads().stream().map(AddBackupTaskDTO.Pad::getPadCode).collect(toList());
        List<Integer> taskStatusList = Arrays.asList(WAIT_EXECUTE.getStatus(), EXECUTING.getStatus());

        int count = padTaskMapper.countByPadCodesStatus(padCodes, taskStatusList);
        if (count >= 1) {
            throw new BasicException(PROCESSING_FAILED.getStatus(), "实例存在执行中的任务");
        }

        int inBackupPadCodeSize = padBackupTaskInfoMapper.countByPadCodesAndTaskStatusList(padCodes, taskStatusList);
        if (inBackupPadCodeSize >= 1) {
            throw new BasicException(PROCESSING_FAILED.getStatus(), "实例已存在备份任务");
        }

        long customerId = param.getCustomerId();
        AddPadTaskDTO addPadTaskDTO = new AddPadTaskDTO();
        addPadTaskDTO.setType(BACKUP_PAD.getType());
        addPadTaskDTO.setStatus(WAIT_EXECUTE.getStatus());
        addPadTaskDTO.setCustomerId(customerId);
        addPadTaskDTO.setPadCodes(padCodes);
        String sourceCode = Optional.ofNullable(param.getTaskSource()).map(SourceTargetEnum::getCode)
                .orElse(SourceTargetEnum.PAAS.getCode());
        addPadTaskDTO.setSourceCode(sourceCode);

        List<PadTask> padTasks = new ArrayList<>(padCodes.size());
        List<AddPadTaskVO> addPadTaskVOS = addPadTaskService(addPadTaskDTO);
        addPadTaskVOS.forEach(addPadTaskVO -> {
            PadTask padTask = new PadTask();
            padTask.setCustomerTaskId(addPadTaskVO.getCustomerTaskId());
            padTask.setStatus(addPadTaskVO.getSubTaskStatus());
            padTask.setPadCode(addPadTaskVO.getPadCode());
            padTasks.add(padTask);
        });

        List<PadBackupTaskInfo> padBackupTaskInfos = new ArrayList<>(padCodes.size());
        param.getPads().forEach(pad -> {
            String padCode = pad.getPadCode();
            AddPadTaskVO addPadTaskVO = addPadTaskVOS.stream()
                    .filter(p -> Objects.equals(padCode, p.getPadCode()))
                    .findFirst().orElse(null);
            if (Objects.isNull(addPadTaskVO)) {
                log.error("addBackupTask error>>>> addPadTaskVO is null addPadTaskVOS:{}", JSON.toJSONString(addPadTaskVOS));
                throw new BasicException(PROCESSING_FAILED);
            }

            PadBackupTaskInfo padBackupTaskInfo = new PadBackupTaskInfo();
            padBackupTaskInfo.setBackupName(pad.getBackupName());
            padBackupTaskInfo.setPadCode(padCode);
            padBackupTaskInfo.setDeviceId(pad.getDeviceId());
            padBackupTaskInfo.setSubTaskId(addPadTaskVO.getSubTaskId());
            padBackupTaskInfo.setPath("");
            padBackupTaskInfo.setCustomerId(customerId);
            padBackupTaskInfo.setBackupType(PadBackupEnum.FULL_BACKUP.getIntValue());
            padBackupTaskInfo.setSpecificationCode(pad.getSpecificationCode());
            padBackupTaskInfos.add(padBackupTaskInfo);
        });

        padBackupTaskInfoMapper.batchInsert(padBackupTaskInfos);
        return padTasks;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<PadTask> addRestoreTask(RestoreBackupTaskDTO param) {
        List<String> padCodes = param.getPads().stream().map(RestoreBackupTaskDTO.Pad::getPadCode).collect(toList());
        List<Integer> taskStatusList = Arrays.asList(WAIT_EXECUTE.getStatus(), EXECUTING.getStatus());

        int count = padTaskMapper.countByPadCodesStatus(padCodes, taskStatusList);
        if (count >= 1) {
            throw new BasicException(PROCESSING_FAILED.getStatus(), "实例存在执行中的任务");
        }

        int inBackupPadCodeSize = padRestoreTaskInfoMapper.countByPadCodesAndTaskStatusList(padCodes, taskStatusList);
        if (inBackupPadCodeSize >= 1) {
            throw new BasicException(PROCESSING_FAILED.getStatus(), "实例已存在备份恢复任务");
        }

        List<Long> backupIds = param.getPads().stream().map(RestoreBackupTaskDTO.Pad::getBackupId).collect(toList());
        List<PadBackupTaskInfo> backupList = padBackupTaskInfoMapper.listById(backupIds);
        long backupIdSize = backupIds.stream().distinct().count();
        if (backupList.size() != backupIdSize) {
            throw new BasicException(PROCESSING_FAILED.getStatus(), "备份数据不存在");
        }

        // 生成恢复任务
        List<PadTask> padTasks = new ArrayList<>(padCodes.size());
        long customerId = param.getCustomerId();
        AddPadTaskDTO addPadTaskDTO = new AddPadTaskDTO();
        addPadTaskDTO.setType(RESTORE_PAD.getType());
        addPadTaskDTO.setStatus(WAIT_EXECUTE.getStatus());
        addPadTaskDTO.setCustomerId(customerId);
        addPadTaskDTO.setPadCodes(padCodes);
        String sourceCode = Optional.ofNullable(param.getTaskSource()).map(SourceTargetEnum::getCode)
                .orElse(SourceTargetEnum.PAAS.getCode());
        addPadTaskDTO.setSourceCode(sourceCode);

        List<AddPadTaskVO> addPadTaskVOS = addPadTaskService(addPadTaskDTO);
        addPadTaskVOS.forEach(addPadTaskVO -> {
            PadTask padTask = new PadTask();
            padTask.setCustomerTaskId(addPadTaskVO.getCustomerTaskId());
            padTask.setStatus(addPadTaskVO.getSubTaskStatus());
            padTask.setPadCode(addPadTaskVO.getPadCode());
            padTasks.add(padTask);
        });

        // 添加恢复任务详情
        List<PadRestoreTaskInfo> padRestoreTaskInfos = new ArrayList<>(padCodes.size());
        param.getPads().forEach(pad -> {
            String padCode = pad.getPadCode();
            AddPadTaskVO addPadTaskVO = addPadTaskVOS.stream()
                    .filter(p -> Objects.equals(padCode, p.getPadCode()))
                    .findFirst().orElse(null);
            if (Objects.isNull(addPadTaskVO)) {
                log.error("addBackupTask error>>>> addPadTaskVO is null addPadTaskVOS:{}", JSON.toJSONString(addPadTaskVOS));
                throw new BasicException(PROCESSING_FAILED);
            }

            PadBackupTaskInfo padBackupTaskInfo = backupList.stream()
                    .filter(backup -> Objects.equals(pad.getBackupId(), backup.getId()))
                    .findFirst().orElse(null);
            if (Objects.isNull(padBackupTaskInfo)) {
                log.error("addBackupTask error>>>> padBackupTaskInfo is null backupList:{}", JSON.toJSONString(backupList));
                throw new BasicException(PROCESSING_FAILED);
            }

            PadRestoreTaskInfo padRestoreTaskInfo = new PadRestoreTaskInfo();
            padRestoreTaskInfo.setRestoreName(padBackupTaskInfo.getBackupName());
            padRestoreTaskInfo.setSubTaskId(addPadTaskVO.getSubTaskId());
            padRestoreTaskInfo.setDeviceId(pad.getDeviceId());
            padRestoreTaskInfo.setPadCode(padCode);
            padRestoreTaskInfo.setBackupId(padBackupTaskInfo.getId());
            padRestoreTaskInfo.setCustomerId(customerId);
            padRestoreTaskInfos.add(padRestoreTaskInfo);
        });

        padRestoreTaskInfoMapper.batchInsert(padRestoreTaskInfos);
        return padTasks;
    }

    /**
     * 实例任务状态
     *
     * @param dto     cms容器任务执行结果
     * @param task    master task
     * @param padTask 实例任务
     * @return Boolean
     */
    private Boolean changeTaskStatusService(ContainerInstanceTaskResultDTO dto, Task task, PadTask padTask) {
        log.info("updateContainerInstanceTaskResult ContainerTaskId={} task type is {}", dto.getMasterTaskId(), task.getType());
        if ((Objects.equals(UPGRADE_IMAGE.getType(), task.getType()) || Objects.equals(REPLACE_PAD.getType(), task.getType())) && (SUCCESS.getStatus().equals(dto.getMasterTaskStatus()))) {
            return true;
        }
        Date endDate = new Date();
        PadTask par = new PadTask();
        par.setStatus(dto.getMasterTaskStatus());
        par.setErrorMsg(dto.getMsg());
        par.setUpdateBy("updateInstanceTask");
        if(StringUtils.isNotBlank(dto.getResult())){
            par.setResult(dto.getResult());
        }
        ArrayList<Integer> filterList = Lists.newArrayList(EXECUTING.getStatus(), WAIT_EXECUTE.getStatus());
        //执行中跟待执行不要设置结束时间
        if (!filterList.contains(dto.getMasterTaskStatus())) {
            par.setEndTime(endDate);
        }
        par.setUpdateTime(endDate);
        // 网存的关机成功任务不回调，关机时会触发同步任务，同步备份任务才回调，还需要兼容CBS版本，低于2.0.73的版本还是走关机回调逻辑
        if(Objects.equals(CONTAINER_NET_STORAGE_OFF.getType(), task.getType()) && SUCCESS.getStatus().equals(dto.getMasterTaskStatus())){
            String deviceCbsInfo = deviceMapper.selectDeviceCbsInfoByPadCode(dto.getPadCode());
            if(StringUtils.isNotBlank(deviceCbsInfo)) {
                int cmp = compareVersions(deviceCbsInfo.trim(), SYNCHRONOUS_TASK_CBS_INFO);
                if (cmp >= 0) {
                    return true;
                }
            }
        }

        int update = padTaskMapper.update(par, new QueryWrapper<PadTask>().lambda().eq(PadTask::getId, padTask.getId()).in(PadTask::getStatus, WAIT_EXECUTE.getStatus(), EXECUTING.getStatus()));

        if (update > ZERO) {
            taskTaskManager.refreshMasterTaskStatus(padTask.getTaskId());
            padTask.setStatus(dto.getMasterTaskStatus());
            padTask.setEndTime(endDate);
            // 适配客户的逻辑，如果是同步任务并且执行中，把关机的任务查询出来并且设置为执行中
            if(Objects.equals(NET_SYNC_BACKUP.getType(), task.getType())
                    && EXECUTING.getStatus().equals(dto.getMasterTaskStatus())){
                PadTask lastlyOffPadTask = padTaskMapper.getLatestPadTaskByType(CONTAINER_NET_STORAGE_OFF.getType(),dto.getPadCode());
                if(Objects.isNull(lastlyOffPadTask)){
                    log.error("changeTaskStatusService query lastlyOffPadTask EXECUTING  is null:{}",JSONUtil.toJsonStr(dto));
                    return false;
                }
                task = taskMapper.getById(lastlyOffPadTask.getTaskId());
                padTask = padTaskMapper.getById(lastlyOffPadTask.getId());
            }

            // 适配客户的逻辑，如果是同步任务并且结束，把关机的任务查询出来并且设置为结束
            if(Objects.equals(NET_SYNC_BACKUP.getType(), task.getType())
                    && (SUCCESS.getStatus().equals(dto.getMasterTaskStatus())
                    || FAIL_ALL.getStatus().equals(dto.getMasterTaskStatus()))){
                PadTask lastlyOffPadTask = padTaskMapper.getLatestPadTaskByType(CONTAINER_NET_STORAGE_OFF.getType(),dto.getPadCode());
                if(Objects.isNull(lastlyOffPadTask)){
                    log.error("changeTaskStatusService query lastlyOffPadTask end is null:{}",JSONUtil.toJsonStr(dto));
                    return false;
                }
                task = taskMapper.getById(lastlyOffPadTask.getTaskId());
                padTask = padTaskMapper.getById(lastlyOffPadTask.getId());
                padTask.setEndTime(new Date());
                task.setStatus(dto.getMasterTaskStatus());
                padTask.setStatus(dto.getMasterTaskStatus());
                if (!SUCCESS.getStatus().equals(dto.getMasterTaskStatus())) {
                    padTask.setErrorMsg("网存同步备份失败");
                }
                // 设置为完成
                padTaskMapper.update(new LambdaUpdateWrapper<PadTask>()
                        .set(PadTask::getStatus,dto.getMasterTaskStatus())
                        .set(PadTask::getErrorMsg,padTask.getErrorMsg())
                        .set(PadTask::getEndTime,padTask.getEndTime())
                        .eq(PadTask::getId,padTask.getId()));

                taskTaskManager.refreshMasterTaskStatus(padTask.getTaskId());
            }
            if(Objects.equals(NET_PAD_OFF.getType(), task.getType())
                    && (SUCCESS.getStatus().equals(dto.getMasterTaskStatus())
                    || FAIL_ALL.getStatus().equals(dto.getMasterTaskStatus()))) {
                PadTask latestOnTask = padTaskMapper.getLatestPadTaskByType(NET_PAD_ON.getType(), dto.getPadCode());
                // 判断最后一次开机任务是否已经达到超时时间，并且状态为执行中

                if (latestOnTask != null && latestOnTask.getTimeoutTime() != null && latestOnTask.getTimeoutTime().before(new Date()) && Objects.equals(latestOnTask.getStatus(), EXECUTING.getStatus())) {
                    // 关机任务成功
                    if (Objects.equals(SUCCESS.getStatus(), dto.getMasterTaskStatus())) {
                        // 设置开机任务为失败
                        task = taskMapper.getById(latestOnTask.getTaskId());
                        padTask = padTaskMapper.getById(latestOnTask.getId());
                        padTask.setEndTime(new Date());
                        task.setStatus(FAIL_ALL.getStatus());
                        padTask.setStatus(FAIL_ALL.getStatus());
                        // 设置为完成
                        padTaskMapper.update(new LambdaUpdateWrapper<PadTask>()
                                .set(PadTask::getStatus,padTask.getStatus())
                                .set(PadTask::getErrorMsg, "开机失败")
                                .set(PadTask::getEndTime,padTask.getEndTime())
                                .eq(PadTask::getId,padTask.getId()));
                        taskTaskManager.refreshMasterTaskStatus(padTask.getTaskId());
                        // 设置实例状态为关机
                        padStatusService.updatePadStatusAndSendPadStatusCallback(Collections.singletonList(padTask.getPadCode()), OFF, padTask.getCustomerId(), "updateContainerInstanceTaskResult");
                    } else {
                        // 关机失败设置实例状态为异常
                        padStatusService.updatePadStatusAndSendPadStatusCallback(Collections.singletonList(padTask.getPadCode()), ABNORMAL, padTask.getCustomerId(), "updateContainerInstanceTaskResult");
                    }
                }
            }
            messageNotifyManager.sendPadTaskStatusChangeMessage(task, padTask);
        }
        return update > ZERO;
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public Task addTaskService(Integer type, Integer status, Long customerId, String sourceCode, String createBy) {
        Task task = new Task();
        task.setType(type);
        task.setStatus(status);
        task.setCustomerId(customerId);
        task.setTaskSource(isNotEmpty(sourceCode) ? sourceCode : SourceTargetEnum.PAAS.getCode());
        task.setCreateBy(isNotEmpty(createBy) ? createBy : String.valueOf(customerId));
        task.setUpdateBy(String.valueOf(customerId));
        taskMapper.insertTask(task);

        task.setUniqueId(IdGenerateUtils.generationMasterUniqueId(task.getId()));
        taskMapper.batchUpdateUniqueId(Collections.singletonList(task));
        return task;
    }

    @Override
    public Task addNewTaskService(Integer type, Integer status, Long customerId, String sourceCode, String createBy, List<String> padCodes) {
        if (CollectionUtils.isEmpty(padCodes)) {
            return null;
        }
        Task task = new Task();
        task.setType(type);
        task.setStatus(status);
        task.setCustomerId(customerId);
        task.setTaskSource(isNotEmpty(sourceCode) ? sourceCode : SourceTargetEnum.PAAS.getCode());
        task.setCreateBy(isNotEmpty(createBy) ? createBy : String.valueOf(customerId));
        task.setUpdateBy(String.valueOf(customerId));
        taskMapper.insertTask(task);

        task.setUniqueId(IdGenerateUtils.generationMasterUniqueId(task.getId()));
        taskMapper.batchUpdateUniqueId(Collections.singletonList(task));
        return task;
    }

    @Override
    public List<AddPadTaskVO> addPadTaskService(AddPadTaskDTO dto) {
        List<String> failPads = new ArrayList<>();
        if (dto.getFilterExecutionTask()) {
            failPads = padTaskMapper.getFilterExecutionTaskPads(dto.getType(), dto.getPadCodes());
        }
        if (isNotEmpty(failPads) && failPads.size() == dto.getPadCodes().size()) {
            return null;
        }

        Long customerId = dto.getCustomerId();
        List<String> finalFailPads = failPads;

        //云机编号操作50，则进行日志输出
        if (dto.getPadCodes().size() >= 50) {
            log.info("云机编号超过50：{},hasOpenTaskDup:{}", JSON.toJSONString(dto), hasOpenTaskDup);
        }

        //查询待执行和执行中的任务
        Map<String, HasPadTaskVo> hasPadTaskVoMap;
        if (hasOpenTaskDup && hasInstructions(dto.getType())) {
            hasPadTaskVoMap = setHasPadTaskVoMap(dto.getPadCodes(), finalFailPads);
        } else {
            hasPadTaskVoMap = MapUtil.empty();
        }

        //获取请求参数中的padCodes,此处添加动态配置开关，如存在并发情况则进行拦截
        List<AddPadTaskVO> addPadTaskVOList = removeRepeatPadCode(dto.getPadCodes(), dto.getType(), customerId,hasPadTaskVoMap,dto.getQueueContentJSON());

        Map<String, HasPadTaskVo> finalHasPadTaskVoMap = hasPadTaskVoMap;
        return newTransactionTemplate.execute(transactionStatus -> {
            //主任务
            Task masterTask = addNewTaskService(dto.getType(), dto.getStatus(), customerId, dto.getSourceCode(), dto.getCreateBy(), dto.getPadCodes());
            //子任务 - p_pad_task
            List<PadTask> subTaskList = dto.getPadCodes().stream().filter(padCode -> !finalFailPads.contains(padCode)).map(padCode -> {
                PadTask padTask = new PadTask();
                padTask.setPadCode(padCode);
                padTask.setTaskId(masterTask.getId());
                padTask.setStatus(dto.getStatus());
                padTask.setFileId(dto.getFileId());
                padTask.setImageId(dto.getImageId());
                padTask.setWipeData(dto.getWipeData());
                padTask.setLastImageId(dto.getLastImageId());
                padTask.setTaskContent(dto.getTaskContent());
                padTask.setCreateBy(isNotEmpty(dto.getCreateBy()) ? dto.getCreateBy() : String.valueOf(customerId));
                if (Objects.nonNull(dto.getTimeout())) {
                    padTask.setTimeoutTime(Date.from(LocalDateTime.now().plusSeconds(dto.getTimeout()).atZone(ZoneId.systemDefault()).toInstant()));
                }

                //这种一般属于直发的任务
                if(TaskStatusConstants.EXECUTING.getStatus().equals(padTask.getStatus())){
                    padTask.setStartTime(new Date());
                    TaskTimeoutConfig taskTimeoutConfig = taskTimeoutConfigMapper.selectOne(new QueryWrapper<>(TaskTimeoutConfig.class)
                            .eq("task_type",dto.getType())
                            .eq("delete_flag",0).last("limit 1"));
                    Long timeoutMillisecond = 25000L;
                    if(taskTimeoutConfig != null){
                        timeoutMillisecond = taskTimeoutConfig.getTimeoutMillisecond();
                    }
                    LocalDateTime timeoutTime = LocalDateTime.now().plusSeconds(timeoutMillisecond / 1000);
                    padTask.setTimeoutTime(Date.from(timeoutTime.atZone(ZoneId.systemDefault()).toInstant()));
                }

                //如果是下载指令，则从redis获取自增ID
                if (DOWNLOAD_APP.getType().equals(dto.getType()) || 
                        UNINSTALL_APP.getType().equals(dto.getType()) ||
                        DOWNLOAD_FILE.getType().equals(dto.getType()) ||
                        PAD_SET_NETWORK_PROXY.getType().equals(dto.getType())) {
                    String redisKey = RedisTaskQueueConstants.PAD_TASK_CUSTOMER_ID + "customer_id_" + customerId;
                    padTask.setCustomerTaskId(redisService.increment(redisKey));
                } else {
                    String lockKey = StrUtil.join("_", dto.getType(), padCode);
                    String customerTaskId = redisService.getCacheObject(RedisTaskQueueConstants.PAD_TASK_CUSTOMER_TASK_ID + lockKey);
                    //历史版本是调用：getCustomerTaskId(customerId) 方法，现在是从redis里面获取
                    padTask.setCustomerTaskId(Objects.isNull(customerTaskId) ? null : Integer.parseInt(customerTaskId));
                }
                padTask.setCustomerId(customerId);
                padTask.setCustomerFileId(dto.getCustomerFileId());
                //如果prohibitPadCodeMap不为空 则将这批padCode任务置为失败
                if(CollUtil.isNotEmpty(dto.getProhibitPadCodeMap())){
                    String val = dto.getProhibitPadCodeMap().get(padCode);
                    if(StrUtil.isNotEmpty(val)){
                        padTask.setStatus(FAIL_ALL.getStatus());
                        padTask.setErrorMsg(val);
                        padTask.setStartTime(new Date());
                        padTask.setEndTime(padTask.getStartTime());
                    }
                }
                return padTask;
            }).collect(toList());

            //插入子任务前,查询当前是否存在相同的任务待执行
            List<AddPadTaskVO> hasPadTaskVOS = removeExistTask(subTaskList, masterTask, finalHasPadTaskVoMap);

            if (CollectionUtil.isNotEmpty(subTaskList)) {
                padTaskMapper.insertBatch(subTaskList);
                subTaskList.forEach(subTask -> subTask.setUniqueId(generationSubTaskUniqueId(subTask.getId())));
                padTaskMapper.batchUpdateUniqueId(subTaskList);

                //如果子任务都是失败的 则主任务直接改为失败
                Boolean isUpdateMasterTask = true;
                for(PadTask padTask : subTaskList){
                    if(!FAIL_ALL.getStatus().equals(padTask.getStatus())){
                        isUpdateMasterTask = false;
                        break;
                    }
                }
                if(isUpdateMasterTask){
                    Task taskUpdate = new Task();
                    taskUpdate.setId(masterTask.getId());
                    taskUpdate.setStatus(FAIL_ALL.getStatus());
                    taskMapper.updateById(taskUpdate);
                }
            }

            List<AddPadTaskVO> addPadTaskVOS = new ArrayList<>(subTaskList.size());
            subTaskList.forEach(subTask -> {
                AddPadTaskVO addPadTaskVO = new AddPadTaskVO();
                addPadTaskVO.setPadCode(subTask.getPadCode());
                addPadTaskVO.setMasterTaskId(masterTask.getId());
                addPadTaskVO.setMasterUniqueId(masterTask.getUniqueId());
                addPadTaskVO.setSubTaskId(subTask.getId());
                addPadTaskVO.setSubTaskUniqueId(subTask.getUniqueId());
                addPadTaskVO.setSubTaskStatus(subTask.getStatus());
                addPadTaskVO.setCustomerTaskId(subTask.getCustomerTaskId());
                addPadTaskVOS.add(addPadTaskVO);
            });

            //添加已经存在的任务在返回集合
            if (CollectionUtil.isNotEmpty(hasPadTaskVOS)) {
                addPadTaskVOS.addAll(hasPadTaskVOS);
            }
            //并发被移除的任务，添加到返回集合中
            if (CollectionUtil.isNotEmpty(addPadTaskVOList)) {
                addPadTaskVOS.addAll(addPadTaskVOList);
            }

            // 添加到任务队列进行调度执行
            if (Objects.nonNull(masterTask)) {
                padTaskExecutor.execute(() -> {
                    subTaskList.forEach(padTask -> {
                        if(!FAIL_ALL.getStatus().equals(padTask.getStatus())){
                            taskQueueManager.addPadTask(masterTask, padTask, dto.getQueueContentJSON());
                            //jobTaskQueueManager.executePadTask(padTask.getId());
                        }
                    });
                });
            }
            return addPadTaskVOS;
        });
    }

    /**
     * 添加pad任务 - 拉模式
     *
     * @param dto dto
     * @return taskId
     */
    @Override
    public List<AddPadTaskVO> addPadTaskServicePullMode(AddPadTaskDTO dto) {
        Long customerId = dto.getCustomerId();
        boolean isProhibitParallel = PROHIBIT_PARALLEL_TASK_TYPE.contains(dto.getType());
        //先批量获取指定类型及实例编号待执行、执行中的任务
        List<HasPadTaskVo> execPadTasks = null;
        Map<String, HasPadTaskVo> hasPadTaskVoMap = new HashMap<>();
        if (isProhibitParallel) {
            //这里只会查询禁止并行的任务 因此这里正常情况下一个padCode只会查出一个待执行或者执行中的任务
            execPadTasks = padTaskMapper.getExecTaskPads(dto.getType(), dto.getPadCodes());
            if (CollUtil.isNotEmpty(execPadTasks)) {
                hasPadTaskVoMap = execPadTasks.stream().collect(Collectors.toMap(HasPadTaskVo::getPadCode, hasPadTaskVo -> hasPadTaskVo, (key1, key2) -> key2));
            }
        }
        //拦截相同参数的并发请求 直接返回上一个任务的id 这里的任务是要返回出去的
        List<AddPadTaskVO> addPadTaskVOList = removeRepeatPadCode(dto.getPadCodes(), dto.getType(), customerId, hasPadTaskVoMap, dto.getQueueContentJSON());
        //再次将hasPadTaskVoMap中未在addPadTaskVOList中出现的加入addPadTaskVOList
        if (CollUtil.isNotEmpty(hasPadTaskVoMap)) {
            List<String> excludeAgainPadCodes = new ArrayList<>();
            for (Map.Entry<String, HasPadTaskVo> entry : hasPadTaskVoMap.entrySet()) {
                HasPadTaskVo hasPadTaskVo = entry.getValue();
                if (CollUtil.isNotEmpty(addPadTaskVOList)) {
                    if (!addPadTaskVOList.contains(hasPadTaskVo.getPadCode())) {
                        AddPadTaskVO addPadTaskVO = new AddPadTaskVO();
                        addPadTaskVO.setPadCode(hasPadTaskVo.getPadCode());
                        addPadTaskVO.setCustomerTaskId(hasPadTaskVo.getCustomerTaskId());
                        addPadTaskVOList.add(addPadTaskVO);
                        excludeAgainPadCodes.add(addPadTaskVO.getPadCode());
                    }
                } else {
                    addPadTaskVOList = new ArrayList<>();
                    AddPadTaskVO addPadTaskVO = new AddPadTaskVO();
                    addPadTaskVO.setPadCode(hasPadTaskVo.getPadCode());
                    addPadTaskVO.setCustomerTaskId(hasPadTaskVo.getCustomerTaskId());
                    addPadTaskVOList.add(addPadTaskVO);
                }
            }
            //再次清除无需执行任务的padCode
            dto.setPadCodes((List) CollUtil.subtract(CollUtil.newArrayList(dto.getPadCodes()), excludeAgainPadCodes));
        }
        //校验实例、设备在线状态
        TaskTypeAndChannelEnum taskTypeAndChannelEnum = TaskTypeAndChannelEnum.fromCode(dto.getType());
        String taskChannelEnumCode = taskTypeAndChannelEnum.getChannel();
        if (CollUtil.isNotEmpty(dto.getPadCodes())) {
            if (TaskChannelEnum.GAMESERVER.getCode().equals(taskChannelEnumCode)) {
                //校验实例在线状态
                List<Pad> offlinePadCodes = padMapper.selectList(new QueryWrapper<>(Pad.class).select("pad_code")
                        .in("pad_code", dto.getPadCodes())
                        .in("status", Arrays.asList(0, 1))
                        .eq("online", 0));
                if (CollUtil.isNotEmpty(offlinePadCodes)) {
                    List<String> padCodes = new ArrayList<>();
                    for (Pad pad : offlinePadCodes) {
                        AddPadTaskVO addPadTaskVO = new AddPadTaskVO();
                        addPadTaskVO.setPadCode(pad.getPadCode());
                        addPadTaskVO.setOnline(false);
                        addPadTaskVO.setErrMsg("实例已离线");
                        addPadTaskVOList.add(addPadTaskVO);
                        padCodes.add(pad.getPadCode());
                    }
                    //再次清除离线的padCode
                    dto.setPadCodes((List) CollUtil.subtract(CollUtil.newArrayList(dto.getPadCodes()), padCodes));
                }
            } else if (TaskChannelEnum.CBS.getCode().equals(taskChannelEnumCode)) {
                //检验板卡状态
                List<String> offlinePadCodes = padMapper.getPadCodeByOfflineDevice(dto.getPadCodes());
                if (CollUtil.isNotEmpty(offlinePadCodes)) {
                    for (String padCode : offlinePadCodes) {
                        AddPadTaskVO addPadTaskVO = new AddPadTaskVO();
                        addPadTaskVO.setPadCode(padCode);
                        addPadTaskVO.setOnline(false);
                        addPadTaskVO.setErrMsg("板卡已离线");
                        addPadTaskVOList.add(addPadTaskVO);
                    }
                    //再次清除离线板卡的padCode
                    dto.setPadCodes((List) CollUtil.subtract(CollUtil.newArrayList(dto.getPadCodes()), offlinePadCodes));
                }
            }
        }

        //获取到无需生成任务的padCode
        List<String> excludePadCodes = new ArrayList<>();
        if (CollUtil.isNotEmpty(addPadTaskVOList)) {
            excludePadCodes.addAll(addPadTaskVOList.stream().map(AddPadTaskVO::getPadCode).collect(Collectors.toList()));
        }
        List<AddPadTaskVO> finalAddPadTaskVOList = addPadTaskVOList;
        //创建主任务和子任务
        //校验实例状态 离线则不生成任务但需要返回 (gameserver的任务需要校验实例在线状态、cbs任务需要校验板卡在线状态、bmc任务需要校验arm服务器在线状态)
        //校验部分任务不能并行执行 如果发现待执行和执行中的则直接拒绝
        //状态为待执行的 则加入新的队列
        //给边缘发送任务事件
        return newTransactionTemplate.execute(transactionStatus -> {
            //主任务
            Task masterTask = addNewTaskService(dto.getType(), dto.getStatus(), customerId, dto.getSourceCode(), dto.getCreateBy(), dto.getPadCodes());
            //子任务 - p_pad_task
            List<PadTask> subTaskList = dto.getPadCodes().stream().filter(padCode -> !excludePadCodes.contains(padCode)).map(padCode -> {
                PadTask padTask = new PadTask();
                padTask.setPadCode(padCode);
                padTask.setTaskId(masterTask.getId());
                padTask.setStatus(dto.getStatus());
                padTask.setFileId(dto.getFileId());
                padTask.setImageId(dto.getImageId());
                padTask.setWipeData(dto.getWipeData());
                padTask.setLastImageId(dto.getLastImageId());
                padTask.setTaskContent(dto.getTaskContent());
                padTask.setCreateBy(isNotEmpty(dto.getCreateBy()) ? dto.getCreateBy() : String.valueOf(customerId));
                padTask.setCreateTime(new Date());
                padTask.setUpdateTime(padTask.getCreateTime());
                padTask.setType(dto.getType());
                if (Objects.nonNull(dto.getTimeout())) {
                    log.info("addPadTaskService set timeoutTime: {}", dto.getTimeout());
                    padTask.setTimeoutTime(Date.from(LocalDateTime.now().plusSeconds(dto.getTimeout()).atZone(ZoneId.systemDefault()).toInstant()));
                    padTask.setCustomTimeout(dto.getTimeout());
                }

                //这种一般属于直发的任务 这个逻辑可能不会执行了  暂时先保留
                if (TaskStatusConstants.EXECUTING.getStatus().equals(padTask.getStatus())) {
                    padTask.setStartTime(new Date());
                    TaskTimeoutConfig taskTimeoutConfig = taskTimeoutConfigMapper.selectOne(new QueryWrapper<>(TaskTimeoutConfig.class)
                            .eq("task_type", dto.getType())
                            .eq("delete_flag", 0).last("limit 1"));
                    Long timeoutMillisecond = 28000L;
                    if (taskTimeoutConfig != null) {
                        timeoutMillisecond = taskTimeoutConfig.getTimeoutMillisecond();
                    }
                    LocalDateTime timeoutTime = LocalDateTime.now().plusSeconds(timeoutMillisecond / 1000);
                    padTask.setTimeoutTime(Date.from(timeoutTime.atZone(ZoneId.systemDefault()).toInstant()));
                }

                //如果是下载指令，则从redis获取自增ID
                if (DOWNLOAD_APP.getType().equals(dto.getType()) ||
                        UNINSTALL_APP.getType().equals(dto.getType()) ||
                        DOWNLOAD_FILE.getType().equals(dto.getType()) ||
                        PAD_SET_NETWORK_PROXY.getType().equals(dto.getType())) {
                    String redisKey = RedisTaskQueueConstants.PAD_TASK_CUSTOMER_ID + "customer_id_" + customerId;
                    padTask.setCustomerTaskId(redisService.increment(redisKey));
                } else {
                    String lockKey = StrUtil.join("_", dto.getType(), padCode);
                    String customerTaskId = redisService.getCacheObject(RedisTaskQueueConstants.PAD_TASK_CUSTOMER_TASK_ID + lockKey);
                    //历史版本是调用：getCustomerTaskId(customerId) 方法，现在是从redis里面获取
                    padTask.setCustomerTaskId(Objects.isNull(customerTaskId) ? null : Integer.parseInt(customerTaskId));
                }
                padTask.setCustomerId(customerId);
                padTask.setCustomerFileId(dto.getCustomerFileId());
                //如果prohibitPadCodeMap不为空 则将这批padCode任务置为失败
                if (CollUtil.isNotEmpty(dto.getProhibitPadCodeMap())) {
                    String val = dto.getProhibitPadCodeMap().get(padCode);
                    if (StrUtil.isNotEmpty(val)) {
                        padTask.setStatus(FAIL_ALL.getStatus());
                        padTask.setErrorMsg(val);
                        padTask.setStartTime(new Date());
                        padTask.setEndTime(padTask.getStartTime());
                    }
                }
                padTask.setTaskMode(1);
                return padTask;
            }).collect(toList());

            if (CollectionUtil.isNotEmpty(subTaskList)) {
                padTaskMapper.insertBatch(subTaskList);
                subTaskList.forEach(subTask -> subTask.setUniqueId(generationSubTaskUniqueId(subTask.getId())));
                padTaskMapper.batchUpdateUniqueId(subTaskList);

                //如果子任务都是失败的 则主任务直接改为失败
                Boolean isUpdateMasterTask = true;
                for (PadTask padTask : subTaskList) {
                    if (!FAIL_ALL.getStatus().equals(padTask.getStatus())) {
                        isUpdateMasterTask = false;
                        break;
                    }
                }
                if (isUpdateMasterTask) {
                    Task taskUpdate = new Task();
                    taskUpdate.setId(masterTask.getId());
                    taskUpdate.setStatus(FAIL_ALL.getStatus());
                    taskMapper.updateById(taskUpdate);
                }
            }

            List<AddPadTaskVO> addPadTaskVOS = new ArrayList<>(subTaskList.size());
            subTaskList.forEach(subTask -> {
                AddPadTaskVO addPadTaskVO = new AddPadTaskVO();
                addPadTaskVO.setPadCode(subTask.getPadCode());
                addPadTaskVO.setMasterTaskId(masterTask.getId());
                addPadTaskVO.setMasterUniqueId(masterTask.getUniqueId());
                addPadTaskVO.setSubTaskId(subTask.getId());
                addPadTaskVO.setSubTaskUniqueId(subTask.getUniqueId());
                addPadTaskVO.setSubTaskStatus(subTask.getStatus());
                addPadTaskVO.setCustomerTaskId(subTask.getCustomerTaskId());
                addPadTaskVOS.add(addPadTaskVO);
            });

            //并发被移除的任务，添加到返回集合中
            if (CollectionUtil.isNotEmpty(finalAddPadTaskVOList)) {
                addPadTaskVOS.addAll(finalAddPadTaskVOList);
            }

            // 添加到任务队列进行调度执行
            if (Objects.nonNull(masterTask)) {
                padTaskExecutor.execute(() -> {
                    TraceIdHelper.setTraceId(TraceIdHelper.buildTraceId());
                    List<PadTask> bootOnLimitTaskList = new ArrayList<>();
                    subTaskList.forEach(padTask -> {
                        if (!FAIL_ALL.getStatus().equals(padTask.getStatus())) {
                            try {
                                taskQueueManager.addPadTaskPullMode(masterTask, padTask, dto.getQueueContentJSON(), taskTypeAndChannelEnum.getPriority(), taskChannelEnumCode);
                            } catch (JsonProcessingException e) {
                                log.error("taskQueueManager.addPadTaskPullMode_error e:{}", e.getMessage(), e);
                            } catch (BasicException e) {
                                if (Objects.equals(e.getExceptionCode(), PadExceptionCode.BOOT_ON_NUMBER_LIMIT)) {
                                    log.info("开机限流拦截，padCode:{}", padTask.getPadCode());
                                    bootOnLimitTaskList.add(padTask);
                                } else {
                                    log.error("taskQueueManager.addPadTaskPullMode_error e:{}", e.getMessage(), e);
                                }
                            } catch (Exception e) {
                                log.error("padTaskExecutor.error", e);
                            }
                        }
                    });
                    if (!CollectionUtil.isEmpty(bootOnLimitTaskList)) {
                        ContainerInstanceTaskResultDTO containerInstanceTaskResultMQ = new ContainerInstanceTaskResultDTO();
                        containerInstanceTaskResultMQ.setMasterTaskStatus(FAIL_ALL.getStatus());
                        containerInstanceTaskResultMQ.setMsg("当前集群同时开机实例数达到上限，请稍后再试");
                        bootOnLimitTaskList.forEach(padTask -> {
                            containerInstanceTaskResultMQ.setMasterTaskId(padTask.getId());
                            containerInstanceTaskResultMQ.setPadCode(padTask.getPadCode());
                            containerInstanceTaskResultMQ.setPullMode(Objects.equals(padTask.getTaskMode(), 1));
                            containerInstanceTaskResultMQ.setResult("FAIL");
                            RedisDelayTaskUtils.submitDelayTask(
                                "containerInstanceTaskResultRedisDelayExecutor",
                                JSON.toJSONString(containerInstanceTaskResultMQ),
                                5 * 1000
                            );
                        });
                    }
                });
            }
            return addPadTaskVOS;
        });
    }

    private List<AddPadTaskVO> removeRepeatPadCode(List<String> padCodes, Integer type, Long customerId,Map<String, HasPadTaskVo> hasPadTaskVoMap,String queueContentJSON) {
        //如果是下载指令，则不进行去重
        if (TaskTypeConstants.DOWNLOAD_APP.getType().equals(type) ||
                TaskTypeConstants.UNINSTALL_APP.getType().equals(type) ||
                TaskTypeConstants.DOWNLOAD_FILE.getType().equals(type) ||
                PAD_SET_NETWORK_PROXY.getType().equals(type)) {
            return new ArrayList<>();
        }

        //解析出本次任务的参数
        String paramMd5 = null;
        if(StrUtil.isNotEmpty(queueContentJSON)){
            try{
                PadCMDForwardDTO padCMDForwardDTO = JSONObject.parseObject(queueContentJSON,PadCMDForwardDTO.class);
                if(CollUtil.isNotEmpty(padCMDForwardDTO.getPadInfos())){
                    paramMd5 = MD5Utils.generateMD5(JSONObject.toJSONString(padCMDForwardDTO.getPadInfos().get(0).getData()));
                }
            }catch (Exception e){
                log.warn("removeRepeatPadCode paramMd5 error,json:{}",queueContentJSON,e);
            }
        }

        List<AddPadTaskVO> hasPadTaskVOS = new ArrayList<>();
        Iterator<String> padCodeIterator = padCodes.iterator();
        while (padCodeIterator.hasNext()) {
            String padCode = padCodeIterator.next();
            String cacheKey = StrUtil.join("_", type, padCode);
            String lockKey = StrUtil.join("_", type, padCode,paramMd5);
            Boolean hasLock = redisService.setIfAbsentExpire(lockKey, "1", 5L, TimeUnit.SECONDS);
            if (hasLock) {
                //先获取到锁的数据进行customerTaskId设置，并保存起来，后续3秒内的相同任务不再重新获取customerTaskId，而是延续前面一个任务的customerTaskId
                String redisKey = RedisTaskQueueConstants.PAD_TASK_CUSTOMER_ID + "customer_id_" + customerId;
                redisService.setCacheObject(RedisTaskQueueConstants.PAD_TASK_CUSTOMER_TASK_ID + cacheKey, String.valueOf(redisService.increment(redisKey)), 20L, TimeUnit.SECONDS);
            } else {
                AddPadTaskVO addPadTaskVO = new AddPadTaskVO();
                addPadTaskVO.setPadCode(padCode);
                HasPadTaskVo hasPadTask = hasPadTaskVoMap.get(padCode);
                if (Objects.nonNull(hasPadTask) && ObjectUtil.equals(hasPadTask.getType(), type)) {
                    addPadTaskVO.setCustomerTaskId(hasPadTask.getCustomerTaskId());
                } else {
                    //这里休眠50毫秒
                    ThreadUtil.sleep(50);
                    //获取上一个缓存里面的customerTaskId
                    String customerTaskId = redisService.getCacheObject(RedisTaskQueueConstants.PAD_TASK_CUSTOMER_TASK_ID + cacheKey);
                    addPadTaskVO.setCustomerTaskId(Objects.isNull(customerTaskId) ? null : Integer.parseInt(customerTaskId));
                }
                hasPadTaskVOS.add(addPadTaskVO);
                //清理掉已存在的padCode
                padCodeIterator.remove();
            }
        }
        return hasPadTaskVOS;
    }

    /**
     * 当前指令是否在兼容范围内
     */
    private Boolean hasInstructions(Integer type) {
        //暂时只考虑,实例重启,实例重置
        if (TaskTypeConstants.RESTART.getType().equals(type) ||
                TaskTypeConstants.RESET.getType().equals(type)) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    private Map<String, HasPadTaskVo> setHasPadTaskVoMap(List<String> padCodeList, List<String> finalFailPads) {
        Set<String> padCodes = padCodeList.stream().filter(padCode -> !finalFailPads.contains(padCode)).collect(Collectors.toSet());
        //查询待执行任务
        Map<String, HasPadTaskVo> hasPadTaskVoMap = new HashMap<>(padCodes.size());
        padCodes.forEach(padCode -> {
            HasPadTaskVo hasPadTask = padTaskMapper.getHasPadTask(padCode);
            if (Objects.isNull(hasPadTask)) {
                return;
            }
            hasPadTaskVoMap.put(hasPadTask.getPadCode(), hasPadTask);
        });
        return hasPadTaskVoMap;
    }

    //插入子任务前,查询当前是否存在相同的任务待执行(并把需要移除的任务进行返回)
    private List<AddPadTaskVO> removeExistTask(List<PadTask> subTaskList, Task masterTask, Map<String, HasPadTaskVo> hasPadTaskVoMap) {
        if (Objects.isNull(masterTask)) {
            return Collections.emptyList();
        }
        if (MapUtil.isEmpty(hasPadTaskVoMap)) {
            return Collections.emptyList();
        }
        List<AddPadTaskVO> hasPadTaskVOS = new ArrayList<>();
        Iterator<PadTask> it = subTaskList.iterator();
        while (it.hasNext()) {
            PadTask padTask = it.next();
            //查询待执行任务
            HasPadTaskVo hasPadTask = hasPadTaskVoMap.get(padTask.getPadCode());
            if (Objects.nonNull(hasPadTask) && ObjectUtil.equals(hasPadTask.getType(), masterTask.getType())) {
                log.info("匹配到存在连续任务正在进行中，无法添加相同的指令：{}", JSON.toJSONString(padTask));
                //如果相同则从集合中移除，不进行入库操作
                it.remove();
                //包原来的数据保存起来进行返回
                AddPadTaskVO addPadTaskVO = new AddPadTaskVO();
                addPadTaskVO.setPadCode(hasPadTask.getPadCode());
                addPadTaskVO.setMasterTaskId(masterTask.getId());
                addPadTaskVO.setMasterUniqueId(masterTask.getUniqueId());
                addPadTaskVO.setSubTaskId(hasPadTask.getPadTaskId());
                addPadTaskVO.setSubTaskUniqueId(hasPadTask.getPadTaskUniqueId());
                addPadTaskVO.setSubTaskStatus(hasPadTask.getStatus());
                addPadTaskVO.setCustomerTaskId(hasPadTask.getCustomerTaskId());
                hasPadTaskVOS.add(addPadTaskVO);
            }
        }
        return hasPadTaskVOS;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<AddDeviceTaskVO> addDeviceTaskService(AddDeviceTaskDTO dto) {
        long customerId = dto.getCustomerId();
        //主任务
        Task masterTask = addTaskService(dto.getType(), dto.getStatus(), customerId, dto.getTaskSource(), dto.getCreateBy());

        //子任务 - p_pad_task
        List<String> deviceCodes = new ArrayList<>();
        int num = dto.getDeviceCodes().size();
        Integer startCustomerTaskId = getMultipleCustomerTaskId(customerId, num);
        AtomicInteger atomicInteger = new AtomicInteger(startCustomerTaskId);

        List<DeviceTask> subTaskList = dto.getDeviceCodes().stream().map(deviceCode -> {
            deviceCodes.add(deviceCode);
            DeviceTask deviceTask = new DeviceTask();
            deviceTask.setDeviceCode(deviceCode);
            deviceTask.setTaskId(masterTask.getId());
            deviceTask.setStatus(dto.getStatus());
            deviceTask.setFileId(dto.getFileId());
            deviceTask.setCreateBy(isNotEmpty(dto.getCreateBy()) ? dto.getCreateBy() : String.valueOf(customerId));
            deviceTask.setCustomerTaskId(atomicInteger.get());
            deviceTask.setTaskContent(dto.getTaskContent());
            deviceTask.setCustomerId(customerId);
            if (isNotEmpty(dto.getRemark())) {
                deviceTask.setRemarks(dto.getRemark());
            }
            deviceTask.setRemarks(dto.getRemark());
            atomicInteger.set(atomicInteger.get() + ONE);
            deviceTask.setStartTime(new Date());
            deviceTask.setTaskContent(dto.getRemark());
            deviceTask.setTaskMode((dto.getPullMode()!=null && dto.getPullMode()) ? 1 : 0);
            return deviceTask;
        }).collect(toList());

        if (CONTAINER_VIRTUALIZE.getType().equals(dto.getType())) {
            deviceTaskMapper.insertBatchDefaultTimeout(subTaskList);
        } else {
            deviceTaskMapper.insertBatch(subTaskList);
        }
        subTaskList.forEach(subTask -> subTask.setUniqueId(generationSubTaskUniqueId(subTask.getId())));
        deviceTaskMapper.batchUpdateUniqueId(subTaskList);

        if (CREATE_DEVICE.getType().equals(dto.getType()) || CREATE_DEVICE_SELF_INSPECTION.getType().equals(dto.getType()) || BRUSH_CORE_ARM.getType().equals(dto.getType())) {
            DeviceTask subTask = subTaskList.get(0);
            AddDeviceTaskVO addDeviceTaskVO = new AddDeviceTaskVO();
            addDeviceTaskVO.setDeviceCode(subTask.getDeviceCode());
            addDeviceTaskVO.setSubTaskId(subTask.getId());
            addDeviceTaskVO.setCustomerTaskId(subTask.getCustomerTaskId());
            addDeviceTaskVO.setDeviceIp(dto.getTaskContent());
            addDeviceTaskVO.setMasterTaskId(masterTask.getId());
            addDeviceTaskVO.setCustomerId(customerId);
            return Collections.singletonList(addDeviceTaskVO);
        }else{
            SelectByDeviceCodesDTO par = new SelectByDeviceCodesDTO();
            par.setDeviceCodes(deviceCodes);
            List<Device> deviceList = deviceService.selectByDeviceCodes(par);

            if (CollUtil.isEmpty(deviceList)) {
                log.error("根据设备编号查询设备信息为空，dto,deviceCodes:{}", JSON.toJSONString(dto), JSON.toJSONString(deviceCodes));
                return null;
            }

            Map<String, Device> deviceMap;
            try {
                deviceMap = deviceList.stream().collect(Collectors.toMap(Device::getDeviceCode, Function.identity()));
            } catch (Throwable e){
                log.error("根据设备编号查询设备信息异常，deviceList:{}", JSON.toJSONString(deviceList), e);
                throw new BasicException("存在异常数据，请检查设置的网段信息");
            }

            // 创建 AddDeviceTaskVO 列表
            List<AddDeviceTaskVO> addDeviceTaskDTOS = subTaskList.stream().map(subTask -> {
                        Device device = deviceMap.get(subTask.getDeviceCode());
                        if (device != null) {
                            AddDeviceTaskVO addDeviceTaskVO = new AddDeviceTaskVO();
                            addDeviceTaskVO.setDeviceCode(subTask.getDeviceCode());
                            addDeviceTaskVO.setSubTaskId(subTask.getId());
                            addDeviceTaskVO.setCustomerTaskId(subTask.getCustomerTaskId());
                            addDeviceTaskVO.setDeviceIp(device.getDeviceIp());
                            addDeviceTaskVO.setDeviceDns(device.getDeviceDns());
                            addDeviceTaskVO.setDeviceNetmask(device.getDeviceNetmask());
                            addDeviceTaskVO.setDeviceOutCode(device.getDeviceOutCode());
                            addDeviceTaskVO.setArmServerCode(device.getArmServerCode());
                            addDeviceTaskVO.setMasterTaskId(masterTask.getId());
                            addDeviceTaskVO.setCustomerId(customerId);
                            return addDeviceTaskVO;
                        }
                        return null;
                    }).filter(Objects::nonNull) // 过滤掉 deviceMap 中没有对应设备的任务
                    .collect(Collectors.toList());
            return addDeviceTaskDTOS;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateTaskService(UpdateTaskDTO updateTaskDTO) {
        //主任务
        Task task = taskMapper.selectById(updateTaskDTO.getId());

        Task updateTask = new Task();
        updateTask.setId(updateTaskDTO.getId());
        updateTask.setStatus(updateTaskDTO.getStatus());
        updateTask.setUpdateTime(new Date());
        int row = taskMapper.updateById(updateTask);

        //子任务 - p_pad_task
        if (task.getType() < NumberConsts.TWO_THOUSAND) {
            PadTask updatePadTask = new PadTask();
            updatePadTask.setStatus(updateTaskDTO.getStatus());
            updatePadTask.setUpdateTime(new Date());
            padTaskMapper.update(updatePadTask, new QueryWrapper<PadTask>().eq("task_id", updateTaskDTO.getId()));

            // p_file_task
        } else {
            FileUploadTask updateFileTask = new FileUploadTask();
            updateFileTask.setStatus(updateTaskDTO.getStatus());
            updateFileTask.setUpdatedTime(new Date());
            fileUploadTaskMapper.update(updateFileTask, new QueryWrapper<FileUploadTask>().eq("task_id", updateTaskDTO.getId()));
        }

        return row > ZERO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteTaskService(DeleteTaskDTO deleteTaskDTO) {
        //主任务
        Task task = taskMapper.selectById(deleteTaskDTO.getId());

        Task updateTask = new Task();
        updateTask.setId(deleteTaskDTO.getId());
        updateTask.setDeleteFlag(Boolean.TRUE);
        updateTask.setUpdateTime(new Date());
        int row = taskMapper.updateById(updateTask);

        //子任务
        if (task.belongsToPadTask()) {
            PadTask deletePadTask = new PadTask();
            deletePadTask.setDeleteFlag(Boolean.TRUE);
            deletePadTask.setUpdateTime(new Date());
            padTaskMapper.update(deletePadTask, new QueryWrapper<PadTask>().eq("task_id", deleteTaskDTO.getId()));
        }

        if (task.belongsToFileTask()) {
            FileUploadTask deleteFileTask = new FileUploadTask();
            deleteFileTask.setDeleteFlag(Boolean.TRUE);
            deleteFileTask.setUpdatedTime(new Date());
            fileUploadTaskMapper.update(deleteFileTask, new QueryWrapper<FileUploadTask>().eq("task_id", deleteTaskDTO.getId()));
        }

        return row > ZERO;
    }

    @Override
    public UpdatePadResetAndRestartVO updatePadResetAndRestart(UpdatePadTaskDTO updatePadTaskDTO) {
        QueryWrapper<PadTask> queryWrapper = new QueryWrapper<PadTask>().eq("pad_code", updatePadTaskDTO.getPadCode()).eq("status", TaskStatusConstants.EXECUTING.getStatus()).orderByDesc("create_time").last("limit 1");
        PadTask padTask = padTaskMapper.selectOne(queryWrapper);
        if (ObjectUtil.isNull(padTask)) {
            return null;
        }

        Task task = taskMapper.selectOne(new QueryWrapper<Task>().eq("id", padTask.getTaskId()).in("type", RESTART.getType(), RESET.getType()));
        if (ObjectUtil.isNull(task)) {
            return null;
        }

        //子任务
        PadTask subTask = new PadTask();
        subTask.setId(padTask.getId());
        //重置任务失败
        if (task.getType().equals(RESET.getType()) && updatePadTaskDTO.getResetFlag().equals(Boolean.FALSE)) {
            subTask.setStatus(FAIL_ALL.getStatus());
        } else {
            subTask.setStatus(SUCCESS.getStatus());
        }

        padTaskMapper.updateById(subTask);
        taskTaskManager.refreshMasterTaskStatus(task.getId());

        UpdatePadResetAndRestartVO resultVO = new UpdatePadResetAndRestartVO();
        resultVO.setMasterId(task.getId());
        resultVO.setSubTaskId(subTask.getId());
        resultVO.setTaskType(task.getType());
        resultVO.setSubTaskStatus(subTask.getStatus());
        return resultVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSubTaskStatus(UpdateSubTaskDTO dto) {
//        log.info("updateSubTaskStatus_debug :{}",JSON.toJSONString(dto));
        long masterTaskId = dto.getMasterTaskId();
        long subTaskId = dto.getSubTaskId();
        Integer taskStatus = dto.getSubTaskStatus();
        String msg = dto.getErrorMsg();
        Date endTime = null;
        if (StrUtil.isNotBlank(dto.getResult())) {
            msg = dto.getResult();
        }
        if (!taskStatus.equals(WAIT_EXECUTE.getStatus()) && !taskStatus.equals(EXECUTING.getStatus())) {
            endTime = new Date();
        }

        Date startTime = null;
        if (EXECUTING.getStatus().equals(taskStatus)) {
            startTime = new Date();
        }

        Task masterTask = taskMapper.selectById(masterTaskId);
        if (masterTask == null) {
            return;
        }
        boolean updateSubTaskSuccess = false;
        if (masterTask.belongsToPadTask()) {
            PadTask padTask = new PadTask();
            padTask.setId(subTaskId);
            padTask.setStatus(taskStatus);
            padTask.setResult(dto.getSubTaskResult());
            padTask.setEndTime(endTime);
            padTask.setStartTime(startTime);
            padTask.setErrorMsg(msg);
            updateSubTaskSuccess = padTaskMapper.updateNotEndTaskStatusById(padTask) > 0;
        }

        if (masterTask.belongsToFileTask()) {
            updateSubTaskSuccess = fileUploadTaskMapper.updateNotEndFileStatus(subTaskId, taskStatus, endTime, msg) > 0;
//            log.info("updateSubTaskStatus_debug_1254:{}",JSON.toJSONString(dto));
        }

        if (!updateSubTaskSuccess) {
//            log.info("updateSubTaskStatus_debug_1257:{}",JSON.toJSONString(dto));
            return;
        }

        taskTaskManager.refreshMasterTaskStatus(masterTaskId);
        if (masterTask.belongsToPadTask()) {
//            log.info("updateSubTaskStatus_debug_1262:{}",JSON.toJSONString(dto));
            PadTask padTask = padTaskMapper.getById(subTaskId);
            updateTaskQueue(padTask.getPadCode(), subTaskId, taskStatus);
            // 如果是网存关机不发回调通知
            if(Objects.equals(CONTAINER_NET_STORAGE_OFF.getType(), masterTask.getType()) && EXECUTING.getStatus().equals(dto.getSubTaskStatus())){
                String deviceCbsInfo = deviceMapper.selectDeviceCbsInfoByPadCode(padTask.getPadCode());
                if(StringUtils.isNotBlank(deviceCbsInfo)) {
                    int cmp = compareVersions(deviceCbsInfo.trim(), SYNCHRONOUS_TASK_CBS_INFO);
                    if (cmp >= 0) {
                        return;
                    }
                }
            }

            messageNotifyManager.sendPadTaskStatusChangeMessage(masterTask, padTask);
        }
    }

    private void updateTaskQueue(String padCode, long subTaskId, int taskStatus) {
        if (WAIT_EXECUTE.getStatus() == taskStatus) {
            return;
        }

        if (EXECUTING.getStatus() == taskStatus) {
//            taskQueueManager.startPadTask(padCode, subTaskId);
            return;
        }

        taskQueueManager.removePadTask(padCode, subTaskId);
    }

    @Override
    public List<TaskTimeoutConfig> listTimeoutConfig() {
        return taskTimeoutConfigMapper.listAll();
    }

    @Override
    public PadTaskCallbackVO padTaskByCodeService(String padCode) {
        return taskMapper.selectPadTaskCallbackByCode(padCode);
    }

    @Override
    public void updatePadTaskByWsConnected(UpdatePadTaskByWsDTO dto) {

        String padCode = dto.getPadCode();
//        log.info("updatePadTaskByWsConnected start padCode:{},dto:{}", padCode, JSON.toJSONString(dto));
        PadTask padTask = padTaskMapper.getLatestTask(padCode,UPDATE_PAD_TASK_BY_WS_CONNECTED_TYPE);
//        log.info("updatePadTaskByWsConnected start padCode:{},padTask:{}", padCode, JSON.toJSONString(padTask));
        if (Objects.isNull(padTask)) {
            return;
        }

        // 跳过非执行中的任务
        if (!Objects.equals(padTask.getStatus(), EXECUTING.getStatus())) {
            return;
        }

        // 已过超时时间,由超时调度任务处理
        if (padTask.getTimeoutTime().before(new Date())) {
            return;
        }

        long taskId = padTask.getTaskId();
        Task task = taskMapper.getById(taskId);
        log.info("updatePadTaskByWsConnected start padCode:{},task:{}", padCode, JSON.toJSONString(task));

        if (Objects.equals(RESET.getType(), task.getType())) {
            handlerResetTaskByWsConnected(dto, task, padTask);
            return;
        }
        //一键新机
        if (Objects.equals(REPLACE_PAD.getType(), task.getType())) {
            handlerReplacePadTaskByWsConnected(dto, task, padTask);
            return;
        }

        //网存实例开机实例连接
        if (Objects.equals(CONTAINER_NET_STORAGE_ON.getType(), task.getType()) || Objects.equals(NET_PAD_ON.getType(), task.getType())) {
            handlerNetStoragePadOnTaskByWsConnected(dto, task, padTask);
            return;
        }

        if (Objects.equals(RESTART.getType(), task.getType())) {
            handlerRestartTaskByWsConnected(dto, task, padTask);
            return;
        }

        if (Objects.equals(UPDATE_PAD_ANDROID_PROP.getType(), task.getType())) {
            handlerUpdatePadAndroidPropTaskByWsConnected(dto, task, padTask);
            return;
        }

        if (Objects.equals(UPGRADE_IMAGE.getType(), task.getType()) && Objects.isNull(padTask.getContainerTaskId())) {
            handlerUpgradeImageTaskByWsConnected(dto, task, padTask);
        }

        if ((Objects.equals(VIRTUAL_REAL_SWITCH_UPGRADE_IMAGE.getType(), task.getType())
                || Objects.equals(REAL_VIRTUAL_SWITCH_UPGRADE_IMAGE.getType(), task.getType()))
                && Objects.isNull(padTask.getContainerTaskId())) {
            handlerSwitchUpgradeImageTaskByWsConnected(dto, task, padTask);
        }

    }



    private void handlerNetStoragePadOnTaskByWsConnected(UpdatePadTaskByWsDTO dto, Task task, PadTask padTask) {
        ContainerTaskResultVO taskResultVO = new ContainerTaskResultVO();
        taskResultVO.setMasterTaskId(padTask.getId());
        taskResultVO.setMsg("实例开机成功");
        taskResultVO.setMasterTaskStatus(SUCCESS.getStatus());
        updatePadTaskCallbackTypeOn(task,taskResultVO);
    }

    @Override
    public void updateDeviceTaskByWsConnected(UpdatePadTaskByWsDTO dto) {
        String padCode = dto.getPadCode();
//        log.info("updateDeviceTaskByWsConnected start padCode:{},dto:{}", padCode, JSON.toJSONString(dto));
        Device device = deviceService.selectByPadCode(padCode);
//        log.info("updateDeviceTaskByWsConnected start padCode:{},device:{}", device, JSON.toJSONString(device));
        if(device == null){
            return;
        }
        DeviceTask deviceTask = deviceTaskMapper.getLastestTask(device.getDeviceCode());
//        log.info("updateDeviceTaskByWsConnected start padCode:{},deviceTask:{}", deviceTask, JSON.toJSONString(deviceTask));
        if (Objects.isNull(deviceTask)) {
            return;
        }
        // 跳过非执行中的任务
        if (!Objects.equals(deviceTask.getStatus(), EXECUTING.getStatus())) {
            return;
        }
        // 已过超时时间,由超时调度任务处理
        if (deviceTask.getTimeoutTime().before(new Date())) {
            return;
        }

        long taskId = deviceTask.getTaskId();
        Task task = taskMapper.getById(taskId);
        log.info("updatePadTaskByWsConnected start padCode:{},task:{}", padCode, JSON.toJSONString(task));
        if (Objects.equals(DEVICE_RESTART.getType(), task.getType()) || Objects.equals(POWER_RESET.getType(), task.getType())) {
            //如果设备状态为离线，且长连接已建立，说明物理机已经重启成功。则改变任务状态为完成
            if (Objects.equals(DEVICE_INIT.getStatus(), device.getDeviceStatus())) {
                task.setStatus(SUCCESS.getStatus());
                task.setUpdateTime(new Date());
                taskMapper.updateById(task);
                deviceTask.setStatus(SUCCESS.getStatus());
                deviceTask.setEndTime(new Date());
                deviceTask.setUpdateTime(new Date());
                deviceTaskMapper.updateById(deviceTask);
                //修改物理机状态，发送物理机状态变更回调通知消息
                log.info("updateDeviceTaskByWsConnected start padCode:{},deviceTask:{}", padCode, JSON.toJSONString(deviceTask));
                SendDeviceStatusDTO sendDeviceStatusDTO = new SendDeviceStatusDTO();
                sendDeviceStatusDTO.setDeviceCodes(Collections.singletonList(device.getDeviceCode()));
                sendDeviceStatusDTO.setDeviceStatus(DEVICE_SUCCESS.getStatus());
                sendDeviceStatusDTO.setCustomerId(task.getCustomerId());
                deviceService.updateDeviceStatusAndSendDeviceStatusCallback(sendDeviceStatusDTO.getDeviceCodes(), sendDeviceStatusDTO.getDeviceStatus(), sendDeviceStatusDTO.getCustomerId());
                log.info("updateDeviceTaskByWsConnected end");
            }
        }
    }

    /**
     * 处理一键新机任务之后长连接上来的结果.
     * @param dto
     * @param task
     * @param padTask
     */
    private void handlerReplacePadTaskByWsConnected(UpdatePadTaskByWsDTO dto, Task task, PadTask padTask) {
        boolean connected = Boolean.TRUE.equals(dto.getConnected());
        if (!connected && dto.getStartTime() != null) {
//            log.info("handlerReplacePadTaskByWsConnected padCode:{},connected:{} Id:{},startTime:{}", padTask.getPadCode(), connected,padTask.getId(), DateUtil.date(dto.getStartTime()));
            padTaskMapper.setStartTime(padTask.getId(), DateUtil.date(dto.getStartTime()));
            return;
        }
        if (!connected) {
            return;
        }
        long subTaskId = padTask.getId();
        boolean updateTaskStatusSuccess;
        Date endDate = new Date();
            padTask.setStatus(SUCCESS.getStatus());
            padTask.setResult(Constants.RESULT_SUCCESS);
            padTask.setEndTime(endDate);
            updateTaskStatusSuccess = padTaskMapper.updateNotEndTaskStatusById(padTask) > 0;
//            log.info("handlerReplacePadTaskByWsConnected padCode:{},updateTaskStatusSuccess:{}", padTask.getPadCode(), updateTaskStatusSuccess);
            padTask.setStatus(SUCCESS.getStatus());
            try {
                commsCenterManager.updateCommandRecord(subTaskId, CommsConstant.CommsCmdStatus.SUCCESS_COMMS_CMD_RECORD_STATUS);
            } catch (Exception e) {
                log.error("handlerResetTaskByWsConnected error>>>>padTask:{}", JSON.toJSONString(padTask));
            }
        if (!updateTaskStatusSuccess) {
            return;
        }
        taskTaskManager.refreshMasterTaskStatus(padTask.getTaskId());
        messageNotifyManager.sendPadTaskStatusChangeMessage(task, padTask);
    }

    /**
     * 处理虚实切换升级镜像任务之后长连接上来的结果
     * @param dto
     * @param task
     * @param padTask
     */
    private void handlerSwitchUpgradeImageTaskByWsConnected(UpdatePadTaskByWsDTO dto, Task task, PadTask padTask) {
        log.info("handlerSwitchUpgradeImageTaskByWsConnected dto:{},padTask:{}  ", JSON.toJSONString(dto),JSON.toJSONString(padTask));
        boolean connected = Boolean.TRUE.equals(dto.getConnected());
        if (!connected || StrUtil.isEmpty(dto.getImageId())) {
            return;
        }

        long subTaskId = padTask.getId();
        Integer taskStatus = FAIL_ALL.getStatus();
        if (ObjectUtil.isNotEmpty(dto.getImageId()) && ObjectUtil.isNotEmpty(padTask.getImageId()) && dto.getImageId().equals(padTask.getImageId())) {
            taskStatus = SUCCESS.getStatus();
        }
        if (ObjectUtil.isNotEmpty(dto.getImageId())) {
            padTask.setTaskContent("UpgradeImageId:" + dto.getImageId());
        }
        Date endDate = new Date();
        padTask.setStatus(taskStatus);
        padTask.setResult("UpgradeImageId:" + dto.getImageId());
        padTask.setEndTime(endDate);
        boolean updateTaskStatusSuccess = padTaskMapper.updateNotEndTaskStatusById(padTask) > 0;
        log.info("handlerSwitchUpgradeImageTaskByWsConnected padCode:{},taskType:{},updateTaskStatusSuccess:{}",
                padTask.getPadCode(), task.getType(), updateTaskStatusSuccess);

        try {
            commsCenterManager.updateCommandRecord(subTaskId, Objects.equals(taskStatus, SUCCESS.getStatus()) ?
                    CommsConstant.CommsCmdStatus.SUCCESS_COMMS_CMD_RECORD_STATUS : CommsConstant.CommsCmdStatus.FAIL_COMMS_CMD_RECORD_STATUS);
        } catch (Exception e) {
            log.error("handlerSwitchUpgradeImageTaskByWsConnected error>>>>padTask:{}", JSON.toJSONString(padTask));
        }

        if (!updateTaskStatusSuccess) {
            return;
        }

        taskTaskManager.refreshMasterTaskStatus(padTask.getTaskId());
        messageNotifyManager.sendPadTaskStatusChangeMessage(task, padTask);
    }

    private void handlerResetTaskByWsConnected(UpdatePadTaskByWsDTO dto, Task task, PadTask padTask) {
        boolean connected = Boolean.TRUE.equals(dto.getConnected());
        if (!connected && dto.getStartTime() != null) {
            log.info("handlerResetTaskByWsConnected padCode:{},connected:{}", padTask.getPadCode(), connected);
            log.info("handlerResetTaskByWsConnected Id:{},startTime:{}", padTask.getId(), DateUtil.date(dto.getStartTime()));
            padTaskMapper.setStartTime(padTask.getId(), DateUtil.date(dto.getStartTime()));
            return;
        }
        if (!connected) {
            return;
        }

        long subTaskId = padTask.getId();
        boolean updateTaskStatusSuccess;

        Date endDate = new Date();
        // 重置任务失败
        if (dto.getBirth() != null && Boolean.FALSE.equals(dto.getBirth())) {
            padTask.setStatus(FAIL_ALL.getStatus());
            padTask.setResult(Constants.RESULT_FAIL);
            padTask.setEndTime(endDate);
            padTask.setErrorMsg("系统初始化文件未删除");
            updateTaskStatusSuccess = padTaskMapper.updateNotEndTaskStatusById(padTask) > 0;
            log.info("handlerResetTaskByWsConnected padCode:{},updateTaskStatusSuccess:{},birth:{}", padTask.getPadCode(), updateTaskStatusSuccess, dto.getBirth());
            padTask.setStatus(FAIL_ALL.getStatus());
            try {
                commsCenterManager.updateCommandRecord(subTaskId, CommsConstant.CommsCmdStatus.FAIL_COMMS_CMD_RECORD_STATUS);
            } catch (Exception e) {
                log.error("handlerResetTaskByWsConnected error>>>>padTask:{}", JSON.toJSONString(padTask), e);
            }
        } else {
            padTask.setStatus(SUCCESS.getStatus());
            padTask.setResult(Constants.RESULT_SUCCESS);
            padTask.setEndTime(endDate);
            updateTaskStatusSuccess = padTaskMapper.updateNotEndTaskStatusById(padTask) > 0;
            log.info("handlerResetTaskByWsConnected padCode:{},updateTaskStatusSuccess:{}", padTask.getPadCode(), updateTaskStatusSuccess);
            padTask.setStatus(SUCCESS.getStatus());
            try {
                commsCenterManager.updateCommandRecord(subTaskId, CommsConstant.CommsCmdStatus.SUCCESS_COMMS_CMD_RECORD_STATUS);
            } catch (Exception e) {
                log.error("handlerResetTaskByWsConnected error>>>>padTask:{}", JSON.toJSONString(padTask));
            }
        }

        if (!updateTaskStatusSuccess) {
            return;
        }

        taskTaskManager.refreshMasterTaskStatus(padTask.getTaskId());
        messageNotifyManager.sendPadTaskStatusChangeMessage(task, padTask);
    }

    private void handlerRestartTaskByWsConnected(UpdatePadTaskByWsDTO dto, Task task, PadTask padTask) {
        boolean connected = Boolean.TRUE.equals(dto.getConnected());
        if (!connected && dto.getStartTime() != null) {
            padTaskMapper.setStartTime(padTask.getId(), DateUtil.date(dto.getStartTime()));
            return;
        }
        if (!connected) {
            return;
        }

        long subTaskId = padTask.getId();
        padTask.setStatus(SUCCESS.getStatus());
        padTask.setEndTime(new Date());
        boolean updateTaskStatusSuccess = padTaskMapper.updateNotEndTaskStatusById(padTask) > 0;
        log.info("handlerRestartTaskByWsConnected padCode:{},updateTaskStatusSuccess:{}", padTask.getPadCode(), updateTaskStatusSuccess);
        try {
            commsCenterManager.updateCommandRecord(subTaskId, CommsConstant.CommsCmdStatus.SUCCESS_COMMS_CMD_RECORD_STATUS);
        } catch (Exception e) {
            log.error("handlerResetTaskByWsConnected error>>>>padTask:{}", JSON.toJSONString(padTask));
        }

        taskTaskManager.refreshMasterTaskStatus(padTask.getTaskId());
        if (updateTaskStatusSuccess) {
            padTask.setStatus(SUCCESS.getStatus());
            messageNotifyManager.sendPadTaskStatusChangeMessage(task, padTask);
        }
    }

    private void handlerUpgradeImageTaskByWsConnected(UpdatePadTaskByWsDTO dto, Task task, PadTask padTask) {
        boolean connected = Boolean.TRUE.equals(dto.getConnected());
        if (!connected || StrUtil.isEmpty(dto.getImageId())) {
            return;
        }

        long subTaskId = padTask.getId();
        Integer taskStatus = FAIL_ALL.getStatus();
        if (ObjectUtil.isNotEmpty(dto.getImageId()) && ObjectUtil.isNotEmpty(padTask.getImageId()) && dto.getImageId().equals(padTask.getImageId())) {
            taskStatus = SUCCESS.getStatus();
        }
        if (ObjectUtil.isNotEmpty(dto.getImageId())) {
            padTask.setTaskContent("UpgradeImageId:" + dto.getImageId());
        }
        Date endDate = new Date();
        padTask.setStatus(taskStatus);
        padTask.setResult("UpgradeImageId:" + dto.getImageId());
        padTask.setEndTime(endDate);
        boolean updateTaskStatusSuccess = padTaskMapper.updateNotEndTaskStatusById(padTask) > 0;
        log.info("handlerUpgradeImageTaskByWsConnected padCode:{},updateTaskStatusSuccess:{}", padTask.getPadCode(), updateTaskStatusSuccess);
        try {
            commsCenterManager.updateCommandRecord(subTaskId, Objects.equals(taskStatus, SUCCESS.getStatus()) ? CommsConstant.CommsCmdStatus.SUCCESS_COMMS_CMD_RECORD_STATUS : CommsConstant.CommsCmdStatus.FAIL_COMMS_CMD_RECORD_STATUS);
        } catch (Exception e) {
            log.error("handlerUpgradeImageTaskByWsConnected error>>>>padTask:{}", JSON.toJSONString(padTask));
        }

        taskTaskManager.refreshMasterTaskStatus(padTask.getTaskId());
        if (updateTaskStatusSuccess) {
            padTask.setStatus(taskStatus);
            padTask.setEndTime(endDate);
            messageNotifyManager.sendPadTaskStatusChangeMessage(task, padTask);
        }
    }

    private void handlerUpdatePadAndroidPropTaskByWsConnected(UpdatePadTaskByWsDTO dto, Task task, PadTask padTask) {
        boolean connected = Boolean.TRUE.equals(dto.getConnected());
        if (!connected && dto.getStartTime() != null) {
            padTaskMapper.setStartTime(padTask.getId(), DateUtil.date(dto.getStartTime()));
            return;
        }
        if (!connected) {
            return;
        }

        long subTaskId = padTask.getId();
        padTask.setStatus(SUCCESS.getStatus());
        padTask.setEndTime(new Date());
        boolean updateTaskStatusSuccess = padTaskMapper.updateNotEndTaskStatusById(padTask) > 0;
        log.info("handlerUpdatePadAndroidPropTaskByWsConnected padCode:{},updateTaskStatusSuccess:{}", padTask.getPadCode(), updateTaskStatusSuccess);
        try {
            commsCenterManager.updateCommandRecord(subTaskId, CommsConstant.CommsCmdStatus.SUCCESS_COMMS_CMD_RECORD_STATUS);
        } catch (Exception e) {
            log.error("handlerUpdatePadAndroidPropTaskByWsConnected error>>>>padTask:{}", JSON.toJSONString(padTask));
        }

        taskTaskManager.refreshMasterTaskStatus(padTask.getTaskId());
        if (updateTaskStatusSuccess) {
            padTask.setStatus(SUCCESS.getStatus());
            messageNotifyManager.sendPadTaskStatusChangeMessage(task, padTask);
        }
    }

    /**
     * 由于触发黑名单由老黑名单和新黑名单组成 这里需要进行拆分
     * @param padCode
     * @param blackListCMDDTOJson 包含了所有padcode的黑名单
     * @return
     */
    private BlackListCMDDTO buildAppBlackListBlackListCMDDTO(String padCode, String blackListCMDDTOJson){
        BlackListCMDDTO blackListCMDDTO = JSON.parseObject(blackListCMDDTOJson,BlackListCMDDTO.class);
        BlackListCMDDTO padCodeBlackListCMDDTO = BeanUtil.copyProperties(blackListCMDDTO,BlackListCMDDTO.class);
        padCodeBlackListCMDDTO.setPadAppClassifyMap(null);
        padCodeBlackListCMDDTO.setPadBlackMap(null);
        padCodeBlackListCMDDTO.setBlacklists(null);

        List<String> blackPckNames = new ArrayList<>();
        if(CollUtil.isNotEmpty(blackListCMDDTO.getBlacklists())){
            blackPckNames.addAll(blackListCMDDTO.getBlacklists());
        }
        if(CollUtil.isNotEmpty(blackListCMDDTO.getPadAppClassifyMap())){
            List<Long> padAppClassifys = blackListCMDDTO.getPadAppClassifyMap().get(padCode);
            if(CollUtil.isNotEmpty(padAppClassifys) && CollUtil.isNotEmpty(blackListCMDDTO.getPadBlackMap())){
                for(Long appClassifyId : padAppClassifys){
                    List<String> padCodeBlackList = blackListCMDDTO.getPadBlackMap().get(appClassifyId);
                    if(CollUtil.isNotEmpty(padCodeBlackList)){
                        blackPckNames.addAll(padCodeBlackList);
                    }
                }
            }
        }
        padCodeBlackListCMDDTO.setBlacklists(blackPckNames.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(o -> o))), ArrayList::new)));
        return padCodeBlackListCMDDTO;
    }

    @Override
    public long countDeviceTask(TaskTypeAndStatusDTO dto) {
        return taskMapper.countDeviceTask(dto.getTaskType(),dto.getTaskStatusList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updatePadTaskByContainerTask(ContainerPadTaskRequestDTO dto) {
        return padTaskMapper.batchUpdateContainerById(dto.getSuccessList()) > ZERO;
    }

    @Override
    public void saveDeviceInstance(Long taskId, Long subTaskId, TaskTypeEnum taskTypeEnum, VirtualizeDeviceRequest.Device device, VirtualizeDeviceInfoVO virtualizeDeviceInfoVO,Boolean clearContainerData) {
        List<VirtualizeDeviceRequest.Device.Pad> pads = device.getPads();
        if (CollUtil.isEmpty(pads)) {
            return;
        }

        //获取集群配置
        PullEdgeClusterConfigurationDTO pullEdgeClusterConfigurationDTO = new PullEdgeClusterConfigurationDTO();
        pullEdgeClusterConfigurationDTO.setDeviceType(TaskChannelEnum.CBS.getCode());
        pullEdgeClusterConfigurationDTO.setClusterCode(virtualizeDeviceInfoVO.getClusterCode());
        Map<String,String> edgeConfigMap = pullTaskService.edgeClusterConfiguration(pullEdgeClusterConfigurationDTO);
        String harborUrl = null;
        String harborProject = null;
        if(CollUtil.isNotEmpty(edgeConfigMap)){
            harborUrl = edgeConfigMap.get("harbor.server");
            harborProject = edgeConfigMap.get("harbor.project.name");
        }

        List<TaskRelInstanceDetail> taskRelInstanceDetails = new ArrayList<>(pads.size());
        for (int i = 0; i < pads.size(); i++) {
            VirtualizeDeviceRequest.Device.Pad pad = pads.get(i);

            String instanceName = pad.getPadCode();

            TaskRelInstanceDetail taskRelInstanceDetail = new TaskRelInstanceDetail();
            taskRelInstanceDetail.setTaskType(taskTypeEnum.getIntValue());
            taskRelInstanceDetail.setMasterTaskId(taskId);
            taskRelInstanceDetail.setSubTaskId(subTaskId);
            taskRelInstanceDetail.setInstanceName(instanceName);
            taskRelInstanceDetail.setIdentificationCode(taskId+"_"+subTaskId+"_"+instanceName);
            taskRelInstanceDetail.setContainerIndex(i + 1);

            if (pad.getAdi() != null) {
                taskRelInstanceDetail.setAdiJson(JSON.toJSONString(pad.getAdi()));
            }

            if (pad.getDeviceAndroidProps() != null) {
                taskRelInstanceDetail.setDeviceAndroidProp(JSON.toJSONString(pad.getDeviceAndroidProps()));
            }

            taskRelInstanceDetail.setAndroidProp(pad.getAndroidProp());

            NetworkRequest networkRequest = pad.getNetwork();
            taskRelInstanceDetail.setIp(networkRequest.getIp());
            taskRelInstanceDetail.setMaxUplinkBandwidth(networkRequest.getMaxUplinkBandwidth());
            taskRelInstanceDetail.setMaxDownlinkBandwidth(networkRequest.getMaxUplinkBandwidth());
            taskRelInstanceDetail.setDns(networkRequest.getDns());
            taskRelInstanceDetail.setMac(networkRequest.getMac());

            ImageRequest imageRequest = pad.getImage();
            taskRelInstanceDetail.setImageTag(imageRequest.getTag());
            taskRelInstanceDetail.setImageId(imageRequest.getId());

            DisplayRequest displayRequest = pad.getDisplay();
            taskRelInstanceDetail.setWidth(displayRequest.getWidth());
            taskRelInstanceDetail.setHeight(displayRequest.getHeight());
            taskRelInstanceDetail.setDpi(displayRequest.getDpi());
            taskRelInstanceDetail.setFps(displayRequest.getFps());

            SpecRequest specRequest = pad.getSpec();
            taskRelInstanceDetail.setDisk(specRequest.getDisk());
            taskRelInstanceDetail.setCpu(specRequest.getCpu());
            taskRelInstanceDetail.setMemory(specRequest.getMemory());
            //网存开机,写入网存标记以及网存code
            if(Objects.equals(TaskTypeEnum.INSTANCE_NET_WORK_ON,taskTypeEnum)){
                taskRelInstanceDetail.setNetStorageResFlag(1);
                taskRelInstanceDetail.setNetStorageResId(pad.getNetStorageResId());
            }

            String containerProperty = buildContainerProperty(taskRelInstanceDetail,pad,device,harborUrl,harborProject,clearContainerData);
            taskRelInstanceDetail.setContainerProperty(containerProperty);
            taskRelInstanceDetails.add(taskRelInstanceDetail);
        }

        taskRelInstanceDetailMapper.batchInsert(taskRelInstanceDetails);
    }

    /**
     * 构建容器参数
     * @param taskRelInstanceDetail
     * @param pad
     * @param device
     * @return
     */
    private String buildContainerProperty(TaskRelInstanceDetail taskRelInstanceDetail,VirtualizeDeviceRequest.Device.Pad pad,VirtualizeDeviceRequest.Device device,
                                          String harborUrl,String harborProject,Boolean clearContainerData){
        String instanceName = taskRelInstanceDetail.getInstanceName();
        CreatePadBO createPadBO = new CreatePadBO();
        createPadBO.setDeviceIp(device.getDeviceIp());
        createPadBO.setHostname(instanceName);
        createPadBO.setName(instanceName);
        createPadBO.setCpuLimit(taskRelInstanceDetail.getCpu().longValue());
        createPadBO.setMemoryLimit(taskRelInstanceDetail.getMemory().longValue());
        createPadBO.setStorageLimit(taskRelInstanceDetail.getDisk().longValue());
        createPadBO.setIp(taskRelInstanceDetail.getIp());
        String imageRepository = harborUrl + "/" + harborProject + "/" + taskRelInstanceDetail.getImageId();
        createPadBO.setImageRepository(imageRepository);
        createPadBO.setImageTag(taskRelInstanceDetail.getImageTag());
        createPadBO.setAsync(true);
        createPadBO.setExtId(taskRelInstanceDetail.getIdentificationCode());
        createPadBO.setIsolateDisk(Boolean.TRUE.equals(pad.getSpec().getIsolateDisk()));
        createPadBO.setHostStorageSize(device.getInitInformation().getHostStorageSize());
        createPadBO.setContainerConcurrentSize(pad.getSpec().getDisk());
        createPadBO.setContainerIndex(taskRelInstanceDetail.getContainerIndex());
        createPadBO.setWidth(taskRelInstanceDetail.getWidth());
        createPadBO.setHeight(taskRelInstanceDetail.getHeight());
        createPadBO.setFps(taskRelInstanceDetail.getFps());
        createPadBO.setDpi(taskRelInstanceDetail.getDpi());
        createPadBO.setOtherAndroidProp(taskRelInstanceDetail.getAndroidProp());
        CreatePadBO.setDnsInfo(createPadBO, taskRelInstanceDetail.getDns());
        CreatePadBO.setADIInfo(createPadBO, taskRelInstanceDetail.getAdiJson());
        createPadBO.setDeviceAndroidProps(taskRelInstanceDetail.getDeviceAndroidProp());
        createPadBO.setMac(taskRelInstanceDetail.getMac());
        createPadBO.setClearContainerData(clearContainerData);

        String networkDeviceName = pad.getNetwork().getNetworkDeviceName();
        if (StringUtils.isNotBlank(networkDeviceName) && networkDeviceName.contains(".")) {
            int lastIndex = networkDeviceName.lastIndexOf(".");
            String macVlanSuffix = networkDeviceName.substring(lastIndex + 1);
            String macvlanName = "macvlan" + macVlanSuffix;
            createPadBO.setMacvlanName(macvlanName);
        }
        return JSON.toJSONString(createPadBO);
    }

    @Override
    public void saveDeviceInstanceSucc(Long subTaskId, TaskTypeAndChannelEnum taskTypeAndChannelEnum) {
        if(taskTypeAndChannelEnum == null){
            return;
        }
        if(TaskTypeEnum.DEVICE_CREATE.getIntValue().equals(taskTypeAndChannelEnum.getCbsTaskTypeEnum().getIntValue())
                || TaskTypeEnum.INSTANCE_UPGRADE_IMAGE.getIntValue().equals(taskTypeAndChannelEnum.getCbsTaskTypeEnum().getIntValue())
                || TaskTypeEnum.INSTANCE_NET_WORK_ON.getIntValue().equals(taskTypeAndChannelEnum.getCbsTaskTypeEnum().getIntValue())
                || TaskTypeEnum.INSTANCE_REPLACE_PROP.getIntValue().equals(taskTypeAndChannelEnum.getCbsTaskTypeEnum().getIntValue())){

            List<TaskRelInstanceDetail> taskRelInstanceDetails = taskRelInstanceDetailMapper.getBySubTaskId(taskTypeAndChannelEnum.getCbsTaskTypeEnum().getIntValue(),subTaskId);
            if(CollUtil.isNotEmpty(taskRelInstanceDetails)){
                for(TaskRelInstanceDetail taskRelInstanceDetail : taskRelInstanceDetails){
                    TaskRelInstanceDetailImageSucc taskRelInstanceDetailImageSucc = BeanUtil.copyProperties(taskRelInstanceDetail,TaskRelInstanceDetailImageSucc.class);
                    instanceDetailImageSuccService.saveAndClear(taskRelInstanceDetailImageSucc);
                }
            }
        }
    }

    @Override
    public void saveDeviceInstanceSingle(Long taskId, Long subTaskId, TaskTypeEnum taskTypeEnum, TaskRelInstanceDetail taskRelInstanceDetail, Object obj) {
        UnifiedUpgradeImageRequest unifiedUpgradeImageRequest = BeanUtil.copyProperties(obj,UnifiedUpgradeImageRequest.class);
        if(unifiedUpgradeImageRequest == null){
            return;
        }
        UnifiedUpgradeImageRequest.Instance instance = unifiedUpgradeImageRequest.getInstances().get(0);
        TaskRelInstanceDetail taskRelInstanceDetailSave = new TaskRelInstanceDetail();
        taskRelInstanceDetailSave.setTaskType(taskTypeEnum.getIntValue());
        taskRelInstanceDetailSave.setMasterTaskId(taskId);
        taskRelInstanceDetailSave.setSubTaskId(subTaskId);
        taskRelInstanceDetailSave.setInstanceName(instance.getPadCode());
        taskRelInstanceDetailSave.setIdentificationCode(taskId + "_" +subTaskId + "_" + instance.getPadCode());
        taskRelInstanceDetailSave.setAndroidProp(instance.getAndroidProp());
        taskRelInstanceDetailSave.setMac(instance.getMac());

        CreatePadBO createPadBO = JSON.parseObject(taskRelInstanceDetail.getContainerProperty(), CreatePadBO.class);
        taskRelInstanceDetailSave.setImageId(taskRelInstanceDetail.getImageId());
        taskRelInstanceDetailSave.setImageTag(taskRelInstanceDetail.getImageTag());
        if(instance.getImage() != null){
            taskRelInstanceDetailSave.setImageId(instance.getImage().getId());
            taskRelInstanceDetailSave.setImageTag(instance.getImage().getTag());
            Integer lastNum = createPadBO.getImageRepository().lastIndexOf("/");
            if(lastNum >= 0){
                String imageRepository = createPadBO.getImageRepository().substring(0,lastNum + 1);
                createPadBO.setImageRepository(imageRepository + instance.getImage().getId());
            }
        }
        if(instance.getAdi() != null){
            UnifiedUpgradeImageRequest.ADI adi = instance.getAdi();
            createPadBO.setDownloadUrlOfADI(adi.getTemplateUrl());
            createPadBO.setPasswordOfADI(adi.getTemplatePassword());
            if(instance.getAdi().getLayoutWidth() != null){
                createPadBO.setWidth(adi.getLayoutWidth().intValue());
                createPadBO.setHeight(adi.getLayoutHigh().intValue());
                createPadBO.setFps(adi.getLayoutFps().intValue());
                createPadBO.setDpi(adi.getLayoutDpi().intValue());
            }
            taskRelInstanceDetailSave.setAdiJson(JSON.toJSONString(adi));
        }

        if(StrUtil.isNotEmpty(instance.getMac())){
            createPadBO.setMac(instance.getMac());
        }
        createPadBO.setExtId(taskRelInstanceDetailSave.getIdentificationCode());
        createPadBO.setClearContainerData(instance.getClearDiskData());
        taskRelInstanceDetailSave.setContainerProperty(JSON.toJSONString(createPadBO));
        taskRelInstanceDetailMapper.insert(taskRelInstanceDetailSave);
    }

    @Override
    public void offlineCleanTask(PullTaskHealthDTO pullTaskHealthDTO) {
        if(TaskChannelEnum.GAMESERVER.getCode().equals(pullTaskHealthDTO.getDeviceType())){
            //获取该实例所有的执行中的任务
            List<PadTask> runPadTasks = padTaskMapper.selectList(new QueryWrapper<>(PadTask.class)
                    .eq("pad_code",pullTaskHealthDTO.getDeviceCode())
                    .eq("status", EXECUTING.getStatus())
                    .eq("delete_flag",0));
            if(CollUtil.isNotEmpty(runPadTasks)){
                String timeout = DateUtil.offsetSecond(DateUtil.date(), 10).toString();
                Date timeoutDate = DateUtil.parse(timeout, "yyyy-MM-dd HH:mm:ss");
                for(PadTask padTask : runPadTasks){
                    if(DateUtil.compare(timeoutDate, padTask.getTimeoutTime()) < 0){
                        Task task = taskMapper.selectById(padTask.getTaskId());
                        TaskTypeAndChannelEnum taskTypeAndChannelEnum = TaskTypeAndChannelEnum.fromCode(task.getType());
                        if(taskTypeAndChannelEnum == null || !TaskChannelEnum.GAMESERVER.getCode().equals(taskTypeAndChannelEnum.getChannel())){
                            continue;
                        }
                        //修改进行中的任务超时时间为10秒后(早于10秒的用当前值) 再由超时定时任务去处理
                        padTaskMapper.pullModeUpdateTimeout(padTask.getId(),timeoutDate,"设备离线，进行中的任务状态改为超时");
                    }
                }
            }
        }else if(TaskChannelEnum.CBS.getCode().equals(pullTaskHealthDTO.getDeviceType())){
            //获取该实例所有的执行中的任务
            List<PadTask> runPadTasks = padTaskMapper.selectList(new QueryWrapper<>(PadTask.class)
                    .eq("pad_code",pullTaskHealthDTO.getDeviceCode())
                    .eq("status", EXECUTING.getStatus())
                    .eq("delete_flag",0));
            if(CollUtil.isNotEmpty(runPadTasks)){
                String timeout = DateUtil.offsetSecond(DateUtil.date(), 10).toString();
                Date timeoutDate = DateUtil.parse(timeout, "yyyy-MM-dd HH:mm:ss");
                for(PadTask padTask : runPadTasks){
                    if(DateUtil.compare(timeoutDate, padTask.getTimeoutTime()) < 0){
                        Task task = taskMapper.selectById(padTask.getTaskId());
                        TaskTypeAndChannelEnum taskTypeAndChannelEnum = TaskTypeAndChannelEnum.fromCode(task.getType());
                        if(taskTypeAndChannelEnum == null){
                            continue;
                        }
                        if(RESTART.getType().equals(task.getType())
                                || RESET.getType().equals(task.getType())
                                || UPDATE_PAD_ANDROID_PROP.getType().equals(task.getType())
                                || REPLACE_PAD.getType().equals(task.getType())
                                || MODIFY_PROPERTIES_PAD.getType().equals(task.getType())
                                || REPLACE_REAL_ADB.getType().equals(task.getType())
                                || UPGRADE_IMAGE.getType().equals(task.getType())
                                || VIRTUAL_REAL_SWITCH_UPGRADE_IMAGE.getType().equals(task.getType())
                                || REAL_VIRTUAL_SWITCH_UPGRADE_IMAGE.getType().equals(task.getType())){
                            continue;
                        }
                        //修改进行中的任务超时时间为10秒后(早于10秒的用当前值) 再由超时定时任务去处理
                        padTaskMapper.pullModeUpdateTimeout(padTask.getId(),timeoutDate,"设备离线，进行中的任务状态改为超时");
                    }
                }
            }

            //获取板卡编号
            DeviceCustomerVo deviceCustomerVo = deviceMapper.getDeviceCustomerByDeviceIp(pullTaskHealthDTO.getDeviceIp(),pullTaskHealthDTO.getClusterCode());
            if(deviceCustomerVo != null){
                //获取该实例所有的执行中的任务
                List<DeviceTask> runDeviceTasks = deviceTaskMapper.selectList(new QueryWrapper<>(DeviceTask.class)
                        .eq("device_code",deviceCustomerVo.getDeviceCode())
                        .eq("status", EXECUTING.getStatus())
                        .eq("delete_flag",0));
                if(CollUtil.isNotEmpty(runDeviceTasks)){
                    String timeout = DateUtil.offsetSecond(DateUtil.date(), 10).toString();
                    Date timeoutDate = DateUtil.parse(timeout, "yyyy-MM-dd HH:mm:ss");
                    for(DeviceTask deviceTask : runDeviceTasks){
                        if(DateUtil.compare(timeoutDate, deviceTask.getTimeoutTime()) < 0){
                            //会重启类的指令不处理
                            Task task = taskMapper.selectById(deviceTask.getTaskId());
                            if(DEVICE_RESTART.getType().equals(task.getType())
                                    || CONTAINER_DEVICE_DESTROY.getType().equals(task.getType())){
                                continue;
                            }
                            //修改进行中的任务超时时间为10秒后(早于10秒的用当前值) 再由超时定时任务去处理
                            deviceTaskMapper.pullModeUpdateTimeout(deviceTask.getId(),timeoutDate,"设备离线，进行中的任务状态改为超时");
                        }
                    }
                }
            }
        }
    }

    @Override
    @Transactional
    public void updateBmcTask(PullTaskResultDTO pullTaskResultDTO) {
        //创建板卡 任务失败 不处理
        if((CREATE_DEVICE.getType().equals(pullTaskResultDTO.getTaskType()) || CREATE_DEVICE_SELF_INSPECTION.getType().equals(pullTaskResultDTO.getTaskType()))
                && TaskStatusConstants.FAIL_ALL.getStatus().equals(pullTaskResultDTO.getTaskStatus())){
            return;
        }

        DeviceTask deviceTaskUpdate = new DeviceTask();
        deviceTaskUpdate.setStatus(pullTaskResultDTO.getTaskStatus());
        deviceTaskUpdate.setErrorMsg(pullTaskResultDTO.getErrMsg());
        if (!TaskStatusConstants.EXECUTING.getStatus().equals(pullTaskResultDTO.getTaskStatus()) && !TaskStatusConstants.WAIT_EXECUTE.getStatus().equals(pullTaskResultDTO.getTaskStatus())) {
            //todo 这个时间应该改为上报的时间
            deviceTaskUpdate.setEndTime(new Date());
        }
        int update = deviceTaskMapper.update(deviceTaskUpdate, new QueryWrapper<DeviceTask>().lambda().eq(DeviceTask::getId, pullTaskResultDTO.getTaskId()).in(DeviceTask::getStatus, TaskStatusConstants.WAIT_EXECUTE.getStatus(), TaskStatusConstants.EXECUTING.getStatus()));
        if (update > ZERO) {
            DeviceTask deviceTask = deviceTaskMapper.selectById(pullTaskResultDTO.getTaskId());
            if(deviceTask != null){
                refreshDeviceMainTask(pullTaskResultDTO.getTaskId());
            }
        }

        ArmServer armServer = armServerMapper.selectOne(new QueryWrapper<>(ArmServer.class)
                .eq("cluster_code",pullTaskResultDTO.getClusterCode())
                .eq("arm_ip",pullTaskResultDTO.getDeviceIp())
                .eq("delete_flag",0));

        if(armServer == null){
            return;
        }

        if(CREATE_DEVICE.getType().equals(pullTaskResultDTO.getTaskType()) || CREATE_DEVICE_SELF_INSPECTION.getType().equals(pullTaskResultDTO.getTaskType())){
            ArmServer armServerUpdate = new ArmServer();
            armServerUpdate.setId(armServer.getId());
            armServerUpdate.setOnline(ClusterAndNetConstant.ONLINE);
            armServerMapper.updateArmServer(armServerUpdate);

            //拿最近的一条更新
            for (int i=0;i<10;i++){
                if(updateDeviceStatus(armServer,pullTaskResultDTO)){
                    break;
                }
                try {
                    Thread.sleep(50L);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
            }
            return;
        }

        if(SET_GATEWAY.getType().equals(pullTaskResultDTO.getTaskType())){

            return;
        }

        if(POWER_RESET.getType().equals(pullTaskResultDTO.getTaskType())){

            return;
        }
    }

    @Override
    public List<AddPadTaskVO> addGsDirectPadTaskServicePullMode(AddPadTaskDTO addTaskDTO) {
        padTaskExecutor.execute(() -> {
            PadCMDForwardDTO padCMDForwardDTO = JSON.parseObject(addTaskDTO.getQueueContentJSON(), PadCMDForwardDTO.class);
            addTaskDTO.getPadCodes().forEach(padCode -> {
                try {
                    TaskTypeAndChannelEnum taskTypeAndChannelEnum = TaskTypeAndChannelEnum.fromCode(addTaskDTO.getType());
                    if(taskTypeAndChannelEnum != null){
                        // padInfos存了多个实例信息 只发送属于当前实例的数据
                        PadCMDForwardDTO.PadInfoDTO padInfoDTO = padCMDForwardDTO.getPadInfos().stream()
                                .filter(padInfo -> Objects.equals(padInfo.getPadCode(), padCode))
                                .findFirst().orElse(new PadCMDForwardDTO.PadInfoDTO());

                        CommsMessageBodyBO commsMessageBodyBO = new CommsMessageBodyBO();
                        commsMessageBodyBO.setCommand(taskTypeAndChannelEnum.getCommsCommandEnum().getCommand());
                        commsMessageBodyBO.setData(padInfoDTO.getData());
                        //获取每个padCode对应的集群编号
                        PadAndDeviceInfoVO padAndDeviceInfoVO = padMapper.selectPadAndDeviceInfo(padCode);
                        if(padAndDeviceInfoVO != null){
                            taskQueueManager.gsEdgeEventNotice(padAndDeviceInfoVO.getClusterCode(), padCode,padAndDeviceInfoVO.getPadIp(), commsMessageBodyBO);
                        }
                    }
                } catch (Exception e) {
                    log.error("taskQueueManager.addGsDirectPadTaskServicePullMode_error e:{}",e.getMessage(),e);
                    throw new RuntimeException(e);
                }
            });
        });
        return null;
    }

    @Override
    public List<AddPadTaskVO> addStandardGsDirectPadTaskServicePullMode(AddPadTaskDTO addTaskDTO) {
        padTaskExecutor.execute(() -> {
            PadCMDForwardDTO padCMDForwardDTO = JSON.parseObject(addTaskDTO.getQueueContentJSON(), PadCMDForwardDTO.class);
            addTaskDTO.getPadCodes().forEach(padCode -> {
                try {
                    TaskTypeAndChannelEnum taskTypeAndChannelEnum = TaskTypeAndChannelEnum.fromCode(addTaskDTO.getType());
                    if(taskTypeAndChannelEnum != null){
                        // padInfos存了多个实例信息 只发送属于当前实例的数据
                        PadCMDForwardDTO.PadInfoDTO padInfoDTO = padCMDForwardDTO.getPadInfos().stream()
                                .filter(padInfo -> Objects.equals(padInfo.getPadCode(), padCode))
                                .findFirst().orElse(new PadCMDForwardDTO.PadInfoDTO());

                        CommsMessageBodyBO commsMessageBodyBO = new CommsMessageBodyBO();
                        commsMessageBodyBO.setCommand(taskTypeAndChannelEnum.getCommsCommandEnum().getCommand());
                        commsMessageBodyBO.setData(padInfoDTO.getData());
                        //获取每个padCode对应的集群编号
                        PadAndDeviceInfoVO padAndDeviceInfoVO = padMapper.selectPadAndDeviceInfo(padCode);
                        if(padAndDeviceInfoVO != null){
                            PullTaskVO.TaskInfo taskInfo = new PullTaskVO.TaskInfo();
                            taskInfo.setTaskType(taskTypeAndChannelEnum.getTaskCode());
                            taskInfo.setPriority(taskTypeAndChannelEnum.getPriority());
                            taskInfo.setCreateTime(new Date().getTime());
                            taskInfo.setTaskParam(commsMessageBodyBO);
                            taskQueueManager.gsEdgeEventNotice(padAndDeviceInfoVO.getClusterCode(), padCode,padAndDeviceInfoVO.getPadIp(), Collections.singletonList(taskInfo));
                        }
                    }
                } catch (Exception e) {
                    log.error("taskQueueManager.addStandardGsDirectPadTaskServicePullMode_error e:{}",e.getMessage(),e);
                    throw new RuntimeException(e);
                }
            });
        });
        return null;
    }

    @Override
    public void updateTaskStatusByContainerTaskId(PadTaskBO padTaskBO, Integer status) {
        List<PadTaskBO.PadSubTaskBO> subTasks = padTaskBO.getSubTasks();
        if (CollUtil.isEmpty(subTasks)) {
            return;
        }
        Task task = taskMapper.getById(padTaskBO.getMasterTaskId());
        for (PadTaskBO.PadSubTaskBO subTask : subTasks) {
            Long subTaskId = subTask.getSubTaskId();
            PadTask padTask = padTaskMapper.getById(subTaskId);
            ContainerInstanceTaskResultDTO dto = new ContainerInstanceTaskResultDTO();
            dto.setMasterTaskId(padTaskBO.getMasterTaskId());
            dto.setMasterTaskStatus(status);
            dto.setPadCode(padTask.getPadCode());
            dto.setPullMode(Objects.equals(padTask.getTaskMode(), 1));
            dto.setResult(SUCCESS.getStatus().equals(status) ? "SUCCESS" : "FAIL");
            changeTaskStatusService(dto, task, padTask);
        }
    }

    private Boolean updateDeviceStatus(ArmServer armServer,PullTaskResultDTO pullTaskResultDTO){
        // find empty device
        Device device = deviceMapper.selectOne(new QueryWrapper<>(Device.class)
                .eq("arm_server_code",armServer.getArmServerCode())
                .eq("delete_flag",0)
                .in("init_status",Arrays.asList(0,2))
                .orderByAsc("id").last("limit 1"));
        //状态成功且result为空的情况 则表示创建板卡自检任务成功
        if(device == null || StrUtil.isEmpty(pullTaskResultDTO.getResult())){
            return true;
        }
        Map<String,Object> map = JSONUtil.parseObj(pullTaskResultDTO.getResult());
        if(map.get("ip") == null || StrUtil.isEmpty(String.valueOf(map.get("ip")))){
            return true;
        }

        Device existsDevice = deviceMapper.selectOne(new QueryWrapper<>(Device.class)
                .eq("arm_server_code",armServer.getArmServerCode())
                .eq("delete_flag",0)
                .eq("init_status",INIT_SUCCESS.getStatus())
                .eq("device_ip", String.valueOf(map.get("ip")))
                .last("limit 1"));
        if (existsDevice != null){
            return true;
        }

        Device deviceUpdate = new Device();
        deviceUpdate.setId(device.getId());
        deviceUpdate.setInitStatus(INIT_SUCCESS.getStatus());
        deviceUpdate.setPadAllocationStatus(PadAllocationStatusConstants.UNALLOCATED.getStatus());
        deviceUpdate.setDeviceIp(String.valueOf(map.get("ip")));
        deviceUpdate.setMacAddress(String.valueOf(map.get("mac")));
        deviceUpdate.setPosition(String.valueOf(map.get("position")));
        deviceUpdate.setNodeId(String.valueOf(map.get("node_id")));
        deviceUpdate.setDeviceOutCode(String.valueOf(map.get("card_id")));
        deviceUpdate.setDeviceStatus(Constants.DEVICE_STATUS_INIT_SUCCESS);
        int count = deviceMapper.update(deviceUpdate,new QueryWrapper<>(Device.class).eq("id",device.getId()).in("init_status",Arrays.asList(0,2)));
        return count > 0 ? true:false;
    }

    /**
     * 比较两个版本号，v1 < v2 返回负数，v1 = v2 返回 0，v1 > v2 返回正数
     * @param version1
     * @param version2
     * @return
     */
    private int compareVersions(String version1, String version2) {
        if (version1 == null || version1.isEmpty() || version2 == null || version2.isEmpty()) {
            throw new IllegalArgumentException("Version strings cannot be null or empty.");
        }
        String[] parts1 = version1.split("\\.");
        String[] parts2 = version2.split("\\.");
        int length = Math.max(parts1.length, parts2.length);
        for (int i = 0; i < length; i++) {
            int v1 = (i < parts1.length) ? Integer.parseInt(parts1[i]) : 0;
            int v2 = (i < parts2.length) ? Integer.parseInt(parts2[i]) : 0;
            if (v1 != v2) {
                return v1 < v2 ? -1 : 1;
            }
        }
        return 0;
    }

    public TaskService(TaskFileManager taskFileManager, TaskTaskManager taskTaskManager, CommsCenterManager commsCenterManager,
                       MessageNotifyManager messageNotifyManager, TaskMapper taskMapper, PadTaskMapper padTaskMapper,
                       FileUploadTaskMapper fileUploadTaskMapper, TaskTimeoutConfigMapper taskTimeoutConfigMapper,
                       CustomerTaskIdMapper customerTaskIdMapper,
                       TaskQueueManager taskQueueManager, PadBackupTaskInfoMapper padBackupTaskInfoMapper,
                       TaskQueueMapper taskQueueMapper,
                       PadRestoreTaskInfoMapper padRestoreTaskInfoMapper) {
        this.taskFileManager = taskFileManager;
        this.taskTaskManager = taskTaskManager;
        this.commsCenterManager = commsCenterManager;
        this.messageNotifyManager = messageNotifyManager;
        this.taskMapper = taskMapper;
        this.padTaskMapper = padTaskMapper;
        this.fileUploadTaskMapper = fileUploadTaskMapper;
        this.taskTimeoutConfigMapper = taskTimeoutConfigMapper;
        this.customerTaskIdMapper = customerTaskIdMapper;
        this.taskQueueManager = taskQueueManager;
        this.padBackupTaskInfoMapper = padBackupTaskInfoMapper;
        this.padRestoreTaskInfoMapper = padRestoreTaskInfoMapper;
        this.taskQueueMapper = taskQueueMapper;
    }
}

