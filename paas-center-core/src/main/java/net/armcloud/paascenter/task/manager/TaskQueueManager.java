package net.armcloud.paascenter.task.manager;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.callback.service.strategy.task.TaskCallbackContext;
import net.armcloud.paascenter.cms.model.request.InstanceRestartRequest;
import net.armcloud.paascenter.common.client.internal.vo.AddDeviceTaskVO;
import net.armcloud.paascenter.common.core.constant.task.TaskQueueConstants;
import net.armcloud.paascenter.common.core.constant.task.TaskStatusConstants;
import net.armcloud.paascenter.common.core.constant.task.TaskTypeConstants;
import net.armcloud.paascenter.common.core.exception.BasicException;
import net.armcloud.paascenter.common.enums.EdgeClusterConfigurationEnum;
import net.armcloud.paascenter.common.model.dto.api.NetPadSyncDTO;
import net.armcloud.paascenter.common.model.entity.paas.ArmServer;
import net.armcloud.paascenter.common.model.entity.paas.Device;
import net.armcloud.paascenter.common.model.entity.paas.NetStorageResUnit;
import net.armcloud.paascenter.common.model.entity.paas.Pad;
import net.armcloud.paascenter.common.model.entity.task.PadTask;
import net.armcloud.paascenter.common.model.entity.task.Task;
import net.armcloud.paascenter.common.model.entity.task.TaskQueue;
import net.armcloud.paascenter.common.model.mq.callback.PadStatusTaskMessageMQ;
import net.armcloud.paascenter.common.rocketmq.support.DefaultRocketMqProducerWrapper;
import net.armcloud.paascenter.commscenter.model.bo.CommsMessageBodyBO;
import net.armcloud.paascenter.openapi.exception.code.PadExceptionCode;
import net.armcloud.paascenter.openapi.manager.TaskManager;
import net.armcloud.paascenter.openapi.mapper.ArmServerMapper;
import net.armcloud.paascenter.openapi.mapper.DeviceMapper;
import net.armcloud.paascenter.openapi.mapper.PadMapper;
import net.armcloud.paascenter.openapi.model.dto.NetSyncTaskPayloadDTO;
import net.armcloud.paascenter.openapi.model.dto.netstorage.NetStorageDTO;
import net.armcloud.paascenter.openapi.model.dto.netstorage.NetStoragePadCodeDetailDTO;
import net.armcloud.paascenter.openapi.model.dto.netstorage.NetStorageResUnitDeleteDTO;
import net.armcloud.paascenter.openapi.model.vo.PadAndDeviceInfoVO;
import net.armcloud.paascenter.openapi.model.vo.PadDetailsVO;
import net.armcloud.paascenter.openapi.netpadv2.dto.task.PadBootOnRequestDTO;
import net.armcloud.paascenter.openapi.netpadv2.dto.task.PadDelRequestDTO;
import net.armcloud.paascenter.openapi.netpadv2.service.NetPadBootOnManager;
import net.armcloud.paascenter.openapi.service.IEdgeClusterConfigurationService;
import net.armcloud.paascenter.task.enums.TaskChannelEnum;
import net.armcloud.paascenter.task.enums.TaskTypeAndChannelEnum;
import net.armcloud.paascenter.task.manager.executor.TaskParamExecutorStrategyContext;
import net.armcloud.paascenter.task.mapper.PadTaskMapper;
import net.armcloud.paascenter.task.mapper.TaskQueueMapper;
import net.armcloud.paascenter.task.model.dto.PullTaskIncomingDTO;
import net.armcloud.paascenter.task.model.vo.BmcBrandInfo;
import net.armcloud.paascenter.task.model.vo.PullTaskVO;
import okhttp3.*;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static net.armcloud.paascenter.common.core.constant.task.RedisTaskQueueConstants.*;
import static net.armcloud.paascenter.common.core.constant.task.TaskStatusConstants.*;
import static net.armcloud.paascenter.common.core.constant.task.TaskTypeConstants.*;

@Slf4j
@Component
public class TaskQueueManager {
    private final TaskQueueMapper taskQueueMapper;
    private final RedisTemplate<String, String> redisTemplate;
    private final PadMapper padMapper;
    private final PadTaskMapper padTaskMapper;

    private final ArmServerMapper armServerMapper;

    @Autowired
    private DefaultRocketMqProducerWrapper rocketMqProducerService;
    @Value("${mq.pull-task-queue.topic}")
    private String pullTaskQueueTopic;
    private final Long CACHE_QUEUE_TASK_MAX = 9999999999L;
    private final Long CACHE_QUEUE_SCORE_PREFIX_MAX = 999L;

    private final TaskParamExecutorStrategyContext taskParamExecutorStrategyContext;

    @Autowired
    private TaskManager taskManager;
    @Autowired
    private DeviceMapper deviceMapper;
    /**拉模式下 边缘网关事件通知地址*/
    private final String EDGE_TASK_INCOMING_URL = "/api/agent/task/incoming";

    private static final OkHttpClient HTTP_CLIENT;

    static {
        HTTP_CLIENT = new OkHttpClient.Builder()
                .connectTimeout(1000, TimeUnit.MILLISECONDS)
                .writeTimeout(1000, TimeUnit.MILLISECONDS)
                .readTimeout(1000, TimeUnit.MILLISECONDS)
                .build();
    }

    @Autowired
    private IEdgeClusterConfigurationService edgeClusterConfigurationService;
    @Autowired
    private NetPadBootOnManager netPadBootOnManager;
    @Autowired
    private TaskCallbackContext taskCallbackContext;


    public void addPadTask(Task masterTask, PadTask padTask, String contentJson) {
        boolean isExecuting = TaskStatusConstants.EXECUTING.getStatus().equals(padTask.getStatus());
        String padCode = padTask.getPadCode();
        long padTaskId = padTask.getId();
        TaskQueue taskQueue = new TaskQueue();
        taskQueue.setKey(padCode);
        taskQueue.setContentJson(contentJson);
        taskQueue.setCustomerId(masterTask.getCustomerId());
        taskQueue.setTaskType(masterTask.getType());
        taskQueue.setMasterTaskId(masterTask.getId());
        taskQueue.setSubTaskId(padTaskId);
        if(isExecuting){
            taskQueue.setStatus(TaskQueueConstants.Status.EXECUTING.getIntValue());
        }else{
            taskQueue.setStatus(TaskQueueConstants.Status.NOT_EXECUTED.getIntValue());
        }
        taskQueueMapper.insert(taskQueue);

        //执行中的任务 无需再加入队列
        if(isExecuting){
            return;
        }
        String queueKey = PAD_TASK_QUEUE_PREFIX + padCode;
        redisTemplate.opsForList().rightPush(queueKey, String.valueOf(padTaskId));

        String detailKey = PAD_TASK_QUEUE_DETAIL_PREFIX + padTaskId;
        redisTemplate.opsForHash().put(detailKey, TASK_QUEUE_DETAIL_HASH_FIELD, JSON.toJSONString(taskQueue));
        redisTemplate.opsForHash().put(detailKey, TASK_QUEUE_CREATE_TIME_HASH_FIELD, LocalDateTime.now().toString());
    }



    public void removePadTask(String padCode, long padTaskId) {
//        taskQueueMapper.updateStatusByKeyAndSubtaskId(padCode, padTaskId, TaskQueueConstants.Status.COMPLETED.getIntValue());
        String queueKey = PAD_TASK_QUEUE_PREFIX + padCode;

        redisTemplate.opsForList().remove(queueKey, 1, String.valueOf(padTaskId));
        String detailKey = PAD_TASK_QUEUE_DETAIL_PREFIX + padTaskId;
        redisTemplate.delete(detailKey);
    }

    /**
     * 拉模式 - 添加实例任务
     * @param masterTask
     * @param padTask
     * @param contentJson
     * @param priority 任务优先级  数字越大 优先级越高 0-9
     */
    public void addPadTaskPullMode(Task masterTask, PadTask padTask, String contentJson,Integer priority,String taskChannelEnumCode) throws JsonProcessingException {
        if (StrUtil.isNotBlank(contentJson) && contentJson.length() > 500) {
            log.info("addPadTaskPullMode min masterTask:{},padTask:{},priority:{},taskChannelEnumCode:{}",masterTask,padTask,priority,taskChannelEnumCode);
        }else{
            log.info("addPadTaskPullMode masterTask:{},padTask:{},contentJson:{},priority:{},taskChannelEnumCode:{}",masterTask,padTask,contentJson,priority,taskChannelEnumCode);
        }
        boolean isExecuting = TaskStatusConstants.EXECUTING.getStatus().equals(padTask.getStatus());
        String padCode = padTask.getPadCode();
        long padTaskId = padTask.getId();
        TaskQueue taskQueue = new TaskQueue();
        taskQueue.setKey(padCode);
        taskQueue.setContentJson(contentJson);
        taskQueue.setCustomerId(masterTask.getCustomerId());
        taskQueue.setTaskType(masterTask.getType());
        taskQueue.setMasterTaskId(masterTask.getId());
        taskQueue.setSubTaskId(padTaskId);
        if(isExecuting){
            taskQueue.setStatus(TaskQueueConstants.Status.EXECUTING.getIntValue());
        }else{
            taskQueue.setStatus(TaskQueueConstants.Status.NOT_EXECUTED.getIntValue());
        }
        if (!STREAM_ROOM_TASK_TYPES.contains(masterTask.getType())) {
            taskQueueMapper.insert(taskQueue);
        }


        //执行中的任务 无需再加入队列
        if(isExecuting){
            return;
        }

        String edgeEventNoticeKey = null;
        String cacheKey = PAD_TASK_PULL_MODE_QUEUE_PREFIX + taskChannelEnumCode + ":";
        //获取padCode对应的集群编号和板卡ip
        PadAndDeviceInfoVO padAndDeviceInfoVO = padMapper.selectPadAndDeviceInfo(padCode);
        String clusterCode = "";
        String deviceIp = "";
        if(padAndDeviceInfoVO == null &&!Arrays.asList(1201, 1202, 1203, 1204, 1205).contains(masterTask.getType())
        && !TaskTypeConstants.isPadV2Task(masterTask.getType())){ // 非网存2.0任务
            throw new BasicException("未获取到集群配置!");
        }
        if(Objects.equals(masterTask.getType(), TaskTypeConstants.CONTAINER_NET_STORAGE_ON.getType())){
            ObjectMapper objectMapper = new ObjectMapper();
            objectMapper.registerModule(new JavaTimeModule());
            List<NetStorageDTO> netStorageDTOList = objectMapper.readValue(
                    contentJson,
                    objectMapper.getTypeFactory().constructCollectionType(List.class, NetStorageDTO.class)
            );
            Map<String, NetStoragePadCodeDetailDTO> padCodeDetailDTOMap = netStorageDTOList.stream()
                    .flatMap(dto -> dto.getNetStoragePadCodeDetailDTOList().stream()) // 扁平化处理
                    .collect(Collectors.toMap(NetStoragePadCodeDetailDTO::getPadCode, detail -> detail, (v1, v2) -> v1));
            NetStoragePadCodeDetailDTO netStoragePadCodeDetailDTO = padCodeDetailDTOMap.get(padTask.getPadCode());
            PadDetailsVO padDetailsVO = netStoragePadCodeDetailDTO.getPadDetailsVO();
            clusterCode = padDetailsVO.getClusterCode();
            deviceIp = padDetailsVO.getDeviceIp();
        }else if (Objects.equals(masterTask.getType(), TaskTypeConstants.CONTAINER_NET_STORAGE_DELETE.getType())){
            InstanceRestartRequest request = JSON.parseObject(contentJson, InstanceRestartRequest.class);
            Map<String, InstanceRestartRequest.Instance> instanceMap = request.getInstances().stream()
                    .collect(Collectors.toMap(InstanceRestartRequest.Instance::getPadCode, instance -> instance));
            InstanceRestartRequest.Instance instance = instanceMap.get(padCode);
            deviceIp = instance.getDeviceIp();
            clusterCode = instance.getClusterCode();
        }else if(Objects.equals(masterTask.getType(),TaskTypeConstants.CONTAINER_NET_STORAGE_BACKUP.getType())){
            log.info("addPadTaskPullMode_debug_1204 padTask:{}",JSON.toJSONString(padTask));
            ObjectMapper objectMapper = new ObjectMapper();
            List<NetStorageResUnit> netStorageResUnitList = objectMapper.readValue(
                    contentJson,
                    objectMapper.getTypeFactory().constructCollectionType(List.class, NetStorageResUnit.class)
            );
            Map<String, NetStorageResUnit> storageResUnitMap = netStorageResUnitList.stream()
                    .collect(Collectors.toMap(NetStorageResUnit::getNetStorageResUnitCode, instance -> instance));
            NetStorageResUnit netStorageResUnit = storageResUnitMap.get(padCode);
            deviceIp = netStorageResUnit.getDeviceIp();
            clusterCode = netStorageResUnit.getClusterCode();
        } else if(Objects.equals(masterTask.getType(),TaskTypeConstants.CONTAINER_NET_STORAGE_RES_UNIT_DELETE.getType())){
            ObjectMapper objectMapper = new ObjectMapper();
            List<NetStorageResUnitDeleteDTO> netStorageResUnitList = objectMapper.readValue(
                    contentJson,
                    objectMapper.getTypeFactory().constructCollectionType(List.class, NetStorageResUnitDeleteDTO.class)
            );
            Map<String, NetStorageResUnitDeleteDTO> storageResUnitMap = netStorageResUnitList.stream()
                    .collect(Collectors.toMap(NetStorageResUnitDeleteDTO::getResUnitCode, instance -> instance));
            NetStorageResUnitDeleteDTO netStorageResUnit = storageResUnitMap.get(padCode);
            deviceIp = netStorageResUnit.getDeviceIp();
            clusterCode = netStorageResUnit.getClusterCode();
        } else if(Objects.equals(masterTask.getType(),TaskTypeConstants.NET_SYNC_BACKUP.getType())){
            log.info("addPadTaskPullMode_debug_{} padTask:{}",masterTask.getType(),JSON.toJSONString(padTask));
            ObjectMapper objectMapper = new ObjectMapper();
            NetPadSyncDTO netPadSyncDTO = objectMapper.readValue(contentJson, NetPadSyncDTO.class);
            deviceIp = netPadSyncDTO.getDeviceIp();
            clusterCode = netPadSyncDTO.getClusterCode();
        } else if(Objects.equals(masterTask.getType(), NET_PAD_ON.getType())){
            // 网存2.0开机
            try {
                ThreadUtil.safeSleep(50); // FIXME SHIT 休眠50毫秒等待事务提交
            } catch (Exception e) {
                log.error("addPadTaskPullMode sleep error>>>", e);
            }
            List<PadBootOnRequestDTO> padBootOnRequestDTOS = JSONObject.parseArray(contentJson, PadBootOnRequestDTO.class);
            // 按padCode找到对应的参数
            PadBootOnRequestDTO padBootOnRequestDTO = padBootOnRequestDTOS.stream()
                    .filter(o -> Objects.equals(o.getPadCode(), padCode))
                    .findFirst().orElse(new PadBootOnRequestDTO());
            deviceIp = padBootOnRequestDTO.getDeviceIp();
            clusterCode = padBootOnRequestDTO.getClusterCode();
        } else if(Objects.equals(masterTask.getType(), NET_PAD_DEL.getType())){
            // 网存2.0删除
            List<PadDelRequestDTO> padDelRequestDTOList = JSONObject.parseArray(contentJson, PadDelRequestDTO.class);
            // 按padCode找到对应的参数
            PadDelRequestDTO padDelRequestDTO = padDelRequestDTOList.stream()
                    .filter(o -> Objects.equals(o.getPadCode(), padCode))
                    .findFirst().orElse(new PadDelRequestDTO());
            deviceIp = padDelRequestDTO.getDeviceIp();
            clusterCode = padDelRequestDTO.getClusterCode();
        } else {
            clusterCode = padAndDeviceInfoVO.getClusterCode();
            deviceIp = padAndDeviceInfoVO.getDeviceIp();
        }
        if(TaskChannelEnum.GAMESERVER.getCode().equals(taskChannelEnumCode)){
            cacheKey += padCode;
            edgeEventNoticeKey = padCode;
        }else{
            cacheKey += clusterCode + ":" +deviceIp;

            edgeEventNoticeKey = deviceIp;
        }

        if (TaskTypeConstants.isBootOnTask(masterTask.getType()) && !netPadBootOnManager.canBootOn(clusterCode, padCode)) {
            throw new BasicException(PadExceptionCode.BOOT_ON_NUMBER_LIMIT);
        }

        //构造出各个任务类型的任务参数
        Object paramJson = taskParamExecutorStrategyContext.getInstance(taskQueue.getTaskType()).execute(taskQueue);
        PullTaskVO.TaskInfo taskInfo = new PullTaskVO.TaskInfo();
        taskInfo.setTaskId(taskQueue.getSubTaskId());
        taskInfo.setTaskType(masterTask.getType());
        taskInfo.setPriority(priority);
        taskInfo.setCreateTime(padTask.getCreateTime().getTime());
        taskInfo.setTaskParam(paramJson);
        taskInfo.setTimeoutTime(padTask.getTimeoutTime());

        //List<PullTaskVO.TaskInfo> taskInfos = null;
        //直发的指令不存缓存
        TaskTypeAndChannelEnum taskTypeAndChannelEnum = TaskTypeAndChannelEnum.fromCode(masterTask.getType());
        Boolean isSendDirectly = taskTypeAndChannelEnum != null && taskTypeAndChannelEnum.getSendDirectly();
        Object paramObj = null;
        if(!isSendDirectly){
            //1位数(优先级) + 3位数(后10位每达到最大值加1) + 10位数(最小1 最大9999999999)
            Long score1 = padTaskId / CACHE_QUEUE_TASK_MAX;
            padTaskId = padTaskId - (score1*CACHE_QUEUE_TASK_MAX);
            padTaskId = CACHE_QUEUE_TASK_MAX - padTaskId;
            score1 = CACHE_QUEUE_SCORE_PREFIX_MAX - score1;
            String taskQueueSubTaskId = String.valueOf(taskQueue.getSubTaskId());
            String score = priority+ StrUtil.padPre(score1+"", 3, '0') + StrUtil.padPre(padTaskId+"", 10, '0');
            redisTemplate.opsForZSet().add(cacheKey,taskQueueSubTaskId,Double.parseDouble(score));
            String cacheDetailKey = PAD_TASK_DETAIL_PULL_MODE_QUEUE_PREFIX + taskQueueSubTaskId;
            if(TaskChannelEnum.CBS.getCode().equals(taskChannelEnumCode)){
                //cbs端不好处理对象 这里给json字符串
                taskInfo.setTaskParam(JSON.toJSONString(paramJson));
            }
            redisTemplate.opsForValue().setIfAbsent(cacheDetailKey,JSON.toJSONString(taskInfo),2, TimeUnit.HOURS);
        }else{
            //taskInfos =  new ArrayList<>();
            //taskInfos.add(taskInfo);
            paramObj = taskInfo.getTaskParam();
        }

        // 网存同步任务：丢入 Redis 列表（FIFO），不立刻通知边缘,先存到redis中然后控制时间间隔和下发条目
        if (Objects.equals(masterTask.getType(), TaskTypeConstants.NET_SYNC_BACKUP.getType())) {
            NetSyncTaskPayloadDTO netSyncTaskPayloadDTO = new NetSyncTaskPayloadDTO();
            netSyncTaskPayloadDTO.setMasterTask(masterTask);
            netSyncTaskPayloadDTO.setPadTask(padTask);
            netSyncTaskPayloadDTO.setClusterCode(clusterCode);
            netSyncTaskPayloadDTO.setEdgeEventNoticeKey(edgeEventNoticeKey);
            netSyncTaskPayloadDTO.setParamObj(paramObj);

            String subTaskId = String.valueOf(padTask.getId());
            String payload   = JSON.toJSONString(netSyncTaskPayloadDTO);

            ZSetOperations<String,String> zsetOps = redisTemplate.opsForZSet();
            HashOperations<String,String,String> hashOps = redisTemplate.opsForHash();

            // score 用当前毫秒数保证 FIFO，若同 ms 可加微小随机以保证唯一
            double score = System.currentTimeMillis() + ThreadLocalRandom.current().nextDouble();

            // ZADD NET_SYNC:ZSET score subTaskId
            zsetOps.add(NET_SYNC_FIFO_TASKID_QUEUE, subTaskId, score);
            // HSET NET_SYNC:HASH subTaskId payload
            hashOps.put(NET_SYNC_FIFO_TASK_HASH, subTaskId, payload);

            redisTemplate.expire(NET_SYNC_FIFO_TASKID_QUEUE, 1, TimeUnit.DAYS);
            redisTemplate.expire(NET_SYNC_FIFO_TASK_HASH, 1, TimeUnit.DAYS);

            log.info("NET_SYNC_BACKUP 任务已入队 ZSET+HASH，zsetKey={}, hashKey={}, subTaskId={}",
                    NET_SYNC_FIFO_TASKID_QUEUE, NET_SYNC_FIFO_TASK_HASH, subTaskId);
            return;
        }

        //通知边缘 有任务了
        Boolean noticeStatus = edgeEventNotice(taskChannelEnumCode,clusterCode,edgeEventNoticeKey,paramObj);
        if(Boolean.TRUE.equals(isSendDirectly)){
            //直发任务状态直接修改任务状态
            padTaskMapper.updateStatusAndTimeById(padTask.getId(),(noticeStatus != null && noticeStatus)?TaskStatusConstants.SUCCESS.getStatus():TaskStatusConstants.FAIL_ALL.getStatus(),new Date());
        }
    }

    /**
     * 拉模式 - 添加板卡任务
     * @param taskTypeAndChannelEnum 任务类型
     * @param deviceTask 板卡任务信息
     * @param reqObj 任务参数对象
     * @param taskCreateTime 任务创建时间
     */
    public void addDeviceTaskPullMode(TaskTypeAndChannelEnum taskTypeAndChannelEnum, AddDeviceTaskVO deviceTask, Object reqObj, Date taskCreateTime) {
        boolean isExecuting = TaskStatusConstants.EXECUTING.getStatus().equals(deviceTask.getSubTaskStatus());
        String deviceCode = deviceTask.getDeviceCode();
        long subTaskId = deviceTask.getSubTaskId();
        //执行中的任务 无需再加入队列
        if(isExecuting){
            return;
        }

        String edgeEventNoticeKey = null;
        String cacheKey = PAD_TASK_PULL_MODE_QUEUE_PREFIX + taskTypeAndChannelEnum.getChannel() + ":";
        //获取padCode对应的集群编号和板卡ip
        String clusterCode = null;
        String deviceIp = null;
        if(TaskChannelEnum.BMC.getCode().equals(taskTypeAndChannelEnum.getChannel())){
            if("arm".equals(taskTypeAndChannelEnum.getSmallerType())){
                clusterCode = deviceTask.getClusterCode();
                deviceIp = deviceTask.getArmIp();
            }else{
                Device device = deviceMapper.selectOne(new QueryWrapper<>(Device.class).eq("device_code",deviceCode).eq("delete_flag",0));
                if(device != null){
                    ArmServer armServer = armServerMapper.selectByArmServerCode(device.getArmServerCode());
                    if(armServer != null){
                        clusterCode = armServer.getClusterCode();
                        deviceIp = armServer.getArmIp();
                    }
                }
            }
        }else{
            Device device = deviceMapper.selectOne(new QueryWrapper<>(Device.class).eq("device_code",deviceCode).eq("delete_flag",0));
            if(device != null){
                ArmServer armServer = armServerMapper.selectOne(new QueryWrapper<>(ArmServer.class).eq("arm_server_code",device.getArmServerCode()).eq("delete_flag",0).last("limit 1"));
                clusterCode = armServer.getClusterCode();
                deviceIp = device.getDeviceIp();
            }
        }
        if(TaskChannelEnum.CBS.getCode().equals(taskTypeAndChannelEnum.getChannel()) || TaskChannelEnum.BMC.getCode().equals(taskTypeAndChannelEnum.getChannel())){
            cacheKey += clusterCode + ":" + deviceIp;
            edgeEventNoticeKey = deviceIp;
        }
        //1位数(优先级) + 3位数(后10位每达到最大值加1) + 10位数(最小1 最大9999999999)
        Long score1 = subTaskId / CACHE_QUEUE_TASK_MAX;
        subTaskId = subTaskId - (score1*CACHE_QUEUE_TASK_MAX);
        subTaskId = CACHE_QUEUE_TASK_MAX - subTaskId;
        score1 = CACHE_QUEUE_SCORE_PREFIX_MAX - score1;
        //这里存储的任务id会出现两种情况 第一种纯数字 10000 第二种带小类型的 10000_device 因为它们都属于cbs任务 所以通过这种方式来区分是cbs任务中的板卡任务还是实例任务
        String taskQueueSubTaskId = String.valueOf(deviceTask.getSubTaskId());
        if(StrUtil.isNotEmpty(taskTypeAndChannelEnum.getSmallerType())){
            taskQueueSubTaskId += "_"+taskTypeAndChannelEnum.getSmallerType();
        }
        String score = taskTypeAndChannelEnum.getPriority()+ StrUtil.padPre(score1+"", 3, '0') + StrUtil.padPre(subTaskId+"", 10, '0');
        //这目前会有一个极端问题 cbs任务中 操作实例的任务如实例重启 操作板卡的任务如重启板卡 两者存的是不同的子任务表 因此可能会出现任务id一样的情况及板卡任务的id比任务重启的小
        //不过这类板卡任务的优先级都要高 所以这里的解决方式直接提升优先级即可 但是最小的优先级要大于cbs实例任务的最大优先级
        redisTemplate.opsForZSet().add(cacheKey,taskQueueSubTaskId,Double.parseDouble(score));
        String cacheDetailKey = PAD_TASK_DETAIL_PULL_MODE_QUEUE_PREFIX + taskQueueSubTaskId;
        //构造出各个任务类型的任务参数
        PullTaskVO.TaskInfo taskInfo = new PullTaskVO.TaskInfo();
        taskInfo.setTaskId(deviceTask.getSubTaskId());
        taskInfo.setTaskType(taskTypeAndChannelEnum.getTaskCode());
        taskInfo.setPriority(taskTypeAndChannelEnum.getPriority());
        taskInfo.setCreateTime(taskCreateTime.getTime());
        if(TaskChannelEnum.CBS.getCode().equals(taskTypeAndChannelEnum.getChannel()) ||
                TaskChannelEnum.BMC.getCode().equals(taskTypeAndChannelEnum.getChannel())){
            //cbs端不好处理对象 这里给json字符串
            if(reqObj instanceof String){
                taskInfo.setTaskParam(reqObj);
            }else{
                taskInfo.setTaskParam(JSON.toJSONString(reqObj));
            }
        }else{
            taskInfo.setTaskParam(reqObj);
        }

        if(TaskChannelEnum.BMC.getCode().equals(taskTypeAndChannelEnum.getChannel())){
            BmcBrandInfo bmcBrandInfo;
            if ((CREATE_DEVICE.getType().equals(taskTypeAndChannelEnum.getTaskCode()) || CREATE_DEVICE_SELF_INSPECTION.getType().equals(taskTypeAndChannelEnum.getTaskCode()))) {
                bmcBrandInfo = armServerMapper.selectBrandInfoByArmServer(deviceCode);
            }else{
                bmcBrandInfo = armServerMapper.selectBrandInfoByDeviceCode(deviceCode);
            }
            taskInfo.setBmcBrandInfo(bmcBrandInfo);
            log.info("addBmcBrandInfo deviceCode:{},bmcBrandInfo:{},cacheDetailKey:{}", deviceCode, bmcBrandInfo, cacheDetailKey);
        }

        redisTemplate.opsForValue().setIfAbsent(cacheDetailKey,JSON.toJSONString(taskInfo),2, TimeUnit.HOURS);

        //通知边缘 有任务了
        edgeEventNotice(taskTypeAndChannelEnum.getChannel(),clusterCode,edgeEventNoticeKey,null);

        //记录参数
        TaskQueue taskQueue = new TaskQueue();
        taskQueue.setKey(deviceTask.getDeviceCode());
        taskQueue.setContentJson(JSON.toJSONString(reqObj));
        taskQueue.setCustomerId(deviceTask.getCustomerId());
        taskQueue.setTaskType(taskTypeAndChannelEnum.getTaskCode());
        taskQueue.setMasterTaskId(deviceTask.getMasterTaskId());
        taskQueue.setSubTaskId(deviceTask.getSubTaskId());
        taskQueue.setStatus(TaskQueueConstants.Status.NOT_EXECUTED.getIntValue());
        taskQueueMapper.insert(taskQueue);
    }

    /**
     * 获取任务
     * cbs任务为板卡ip bmc为arm服务器ip gameserver任务为实例编号
     * @param taskChannelEnum 任务渠道
     * @param clusterCode 集群编号
     * @param deviceIp 设备ip
     * @param deviceCode 设备编号
     * @param num 获取数量
     */
    public PullTaskVO getPadTaskPullMode(TaskChannelEnum taskChannelEnum, String clusterCode, String deviceIp,String deviceCode, Integer num) {
        String cacheKey = PAD_TASK_PULL_MODE_QUEUE_PREFIX + taskChannelEnum.getCode() + ":";
        if(TaskChannelEnum.GAMESERVER.getCode().equals(taskChannelEnum.getCode())){
            cacheKey += deviceCode;
        }else{
            cacheKey += clusterCode + ":" + deviceIp;
        }
        Set<String> taskIdList = redisTemplate.opsForZSet().range(cacheKey,0,(num == null || num <= 0) ? 0 : num - 1);
        List<String> realTaskIdList = new ArrayList<>();

        List<String> removeTaskIdList = new ArrayList<>();
        //从redis队列中清除 然后发送mq异步更新任务状态
        if(CollUtil.isNotEmpty(taskIdList)){
            List<PullTaskVO.TaskInfo> taskInfos =  new ArrayList<>();
            for(String padTaskId : taskIdList){
                String cacheDetailKey = PAD_TASK_DETAIL_PULL_MODE_QUEUE_PREFIX + padTaskId;

                String taskParam = redisTemplate.opsForValue().get(cacheDetailKey);
                if(StrUtil.isEmpty(taskParam)){
                    log.warn("任务详情为空，跳过任务 padTaskId={}", padTaskId);
                    continue;
                }

                //此处加个过滤 如果该任务id已存在 则跳过 防止并发获取
                Boolean padTaskIdExist = redisTemplate.opsForValue().setIfAbsent("lock:getPadTaskPullMode:" + cacheKey + padTaskId,"0",5,TimeUnit.MINUTES);
                if(!padTaskIdExist){
                    log.warn("任务 padTaskId={} 加锁失败，跳过", padTaskId);
                    continue;
                }

                PullTaskVO.TaskInfo taskInfo = JSON.parseObject(taskParam,PullTaskVO.TaskInfo.class);
                if (TaskTypeConstants.isPadV2Task(taskInfo.getTaskType())) {
                    JSONObject paramJsonObj = JSONObject.parseObject(String.valueOf(taskInfo.getTaskParam()));
                    long timeoutSeconds = DateUtil.between(new Date(), taskInfo.getTimeoutTime(), DateUnit.SECOND);
                    // 超时时间给CBS留出20s空余
                    paramJsonObj.put("timeout", timeoutSeconds - 20);
                    taskInfo.setTaskParam(paramJsonObj.toString());
                }
                taskInfos.add(taskInfo);
                TaskTypeAndChannelEnum taskTypeAndChannelEnum = TaskTypeAndChannelEnum.fromCode(taskInfo.getTaskType());
                String taskId = String.valueOf(taskInfo.getTaskId());
                if(StrUtil.isNotEmpty(taskTypeAndChannelEnum.getSmallerType())){
                    taskId = taskId + "_" + taskTypeAndChannelEnum.getSmallerType();
                }
                realTaskIdList.add(taskId);
                removeTaskIdList.add(padTaskId);
            }

            if(CollUtil.isNotEmpty(removeTaskIdList)){
                redisTemplate.opsForZSet().remove(cacheKey,removeTaskIdList.toArray());
                //发mq异步更新任务状态
                Map<String,String> paramMap = new HashMap<>();
                paramMap.put("deviceType",taskChannelEnum.getCode());
                paramMap.put("padTaskIds",JSON.toJSONString(realTaskIdList));
                paramMap.put("taskStatus",String.valueOf(TaskStatusConstants.EXECUTING.getStatus()));
                rocketMqProducerService.producerNormalMessage(pullTaskQueueTopic, JSON.toJSONString(paramMap));
            }

            PullTaskVO pullTaskVO = new PullTaskVO();
            pullTaskVO.setServerTime(DateUtil.format(new Date(),"yyyy-MM-dd HH:mm:ss"));
            pullTaskVO.setTaskInfos(taskInfos);
            pullTaskVO.setSurplusTaskCount(redisTemplate.opsForZSet().size(cacheKey));
            return pullTaskVO;
        }
        return null;
    }





    public Boolean edgeEventNotice(String taskChannelEnumCode, String clusterCode, String key,Object obj){
        return edgeEventNotice(taskChannelEnumCode, clusterCode, key,null,obj);
    }

    public Boolean gsEdgeEventNotice(String clusterCode, String padCode,String padIp, CommsMessageBodyBO commsMessageBodyBO){
        return edgeEventNotice(TaskChannelEnum.GAMESERVER.getCode(), clusterCode, padCode,padIp,commsMessageBodyBO);
    }

    public Boolean gsEdgeEventNotice(String clusterCode, String padCode,String padIp, List<PullTaskVO.TaskInfo> taskInfos){
        return edgeEventNotice(TaskChannelEnum.GAMESERVER.getCode(), clusterCode, padCode,padIp,taskInfos);
    }

    /**
     * 更新任务为执行中
     * @param padTaskId
     */
    public void updatePadTaskStatusRunPullMode(Long padTaskId){
        PadTask padTask = padTaskMapper.getById(padTaskId);
        int padTaskStatus = padTask.getStatus();

        if (WAIT_EXECUTE.getStatus() != padTaskStatus) {
            return;
        }
        taskManager.updateSubTaskStatus(padTask.getTaskId(), padTaskId, EXECUTING.getStatus());
    }

    public Boolean edgeEventNotice(String taskChannelEnumCode, String clusterCode, String key,String padIp,Object obj){
        Response response = null;
        String url = null;
        try {
            //先获取集群网关地址 通过edge_cluster_configuration
            url = edgeClusterConfigurationService.getEdgeClusterConfigurationByKey(clusterCode, EdgeClusterConfigurationEnum.EDGE_API_BASE_URL);
            if (StrUtil.isBlank(url)) {
                log.error("通知边缘机房失败，未找到网关地址，当前集群编码为:{}", clusterCode);
                return false;
            }
            //通知边缘 有任务了
            PullTaskIncomingDTO pullTaskIncomingDTO = new PullTaskIncomingDTO();
            pullTaskIncomingDTO.setClusterCode(clusterCode);
            pullTaskIncomingDTO.setDeviceType(taskChannelEnumCode);
            if(obj != null){
                pullTaskIncomingDTO.setData(JSON.toJSONString(obj));
            }
            if(TaskChannelEnum.GAMESERVER.getCode().equals(taskChannelEnumCode)){
                pullTaskIncomingDTO.setDeviceCode(key);
                if(StrUtil.isNotEmpty(padIp)){
                    pullTaskIncomingDTO.setDeviceIP(padIp);
                }else{
                    Pad pad = padMapper.selectOne(new QueryWrapper<>(Pad.class).select("pad_ip")
                            .eq("pad_code",key)
                            .in("status",Arrays.asList(0,1))
                            .last("limit 1"));
                    if(pad != null){
                        pullTaskIncomingDTO.setDeviceIP(pad.getPadIp());
                    }
                }
            }else{
                pullTaskIncomingDTO.setDeviceIP(key);
            }

            url += EDGE_TASK_INCOMING_URL;
            String pullTaskIncomingDTOJson = JSON.toJSONString(pullTaskIncomingDTO);
            log.debug("edgeEventNotice pullTaskIncomingDTOJson:{}",pullTaskIncomingDTOJson);
            RequestBody requestBody = RequestBody.create(pullTaskIncomingDTOJson,
                    MediaType.parse("application/json; charset=utf-8")
            );
            Request request = new Request.Builder().url(url).post(requestBody).build();
            response = HTTP_CLIENT.newCall(request).execute();
            if (!response.isSuccessful()){
                log.error("通知边缘机房失败，状态码{},异常{},param:{}",response.code(),response.message(), pullTaskIncomingDTOJson);
                return false;
            }
            log.info("通知边缘机房成功 url:{},param:{}", url, pullTaskIncomingDTOJson);
            return true;
        } catch (Exception e) {
            log.error("通知边缘机房异常 key:{},url:{}", key, url, e);
            return false;
        } finally {
            if(response != null){
                response.close();
            }
        }
    }


    public TaskQueueManager(RedisTemplate<String, String> redisTemplate, TaskQueueMapper taskQueueMapper,PadMapper padMapper,PadTaskMapper padTaskMapper,
                            TaskParamExecutorStrategyContext taskParamExecutorStrategyContext,
                            ArmServerMapper armServerMapper) {
        this.redisTemplate = redisTemplate;
        this.taskQueueMapper = taskQueueMapper;
        this.padMapper = padMapper;
        this.padTaskMapper = padTaskMapper;
        this.taskParamExecutorStrategyContext = taskParamExecutorStrategyContext;
        this.armServerMapper = armServerMapper;
    }
}
