package net.armcloud.paascenter.openapi.exception.code;

import net.armcloud.paascenter.common.core.exception.code.ExceptionCode;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum PadExceptionCode implements ExceptionCode {
    /**
     *
     */
    SCREENSHOT_FAIL_EXCEPTION(110001, "截图失败"),
    GENERATE_PREVIEW_FAIL_EXCEPTION(110002, "生成预览图失败"),
    EXECUTE_ADB_FAIL_EXCEPTION(110003, "执行ADB命令失败"),
    EXECUTE_RESTART_FAIL_EXCEPTION(110004, "执行重启命令失败"),
    EXECUTE_REST_FAIL_EXCEPTION(110005, "执行重置命令失败"),
    DOWNLOAD_FILE_FAIL_EXCEPTION(110006, "下载失败"),
    UNINSTALL_APP_FAIL_EXCEPTION(110007, "卸载应用失败"),
    START_APP_FAIL_EXCEPTION(110008, "启动应用失败"),
    RESTART_APP_FAIL_EXCEPTION(110009, "重启应用失败"),
    STOP_APP_FAIL_EXCEPTION(110010, "停止应用失败"),
    EXECUTE_UPDATE_PROPERTIES_FAIL_EXCEPTION(110011, "执行修改属性命令失败"),
    EXECUTE_CMD_TIME_OUT_EXCEPTION(110012, "命令执行超时"),
    CUSTOMER_INFORMATION_ABNORMALITY_WITHOUT_VALID_KEY(110013, "客户信息异常:无有效的密钥"),
    PAD_NOT_ENOUGH_STORAGE_SPACE_EXCEPTION(110020, "实例存储空间不足"),
    PAD_RECORD_SCREENING_EXCEPTION(110021, "当前实例正在录屏中"),
    PAD_NOT_START_RECORD_SCREENING_EXCEPTION(110022, "当前实例未处于录屏中"),
    RECORD_SCREENING_FAIL_EXCEPTION(110023, "录屏失败"),
    NO_INSTANCE_MANIPULATION_PERMISSION(110024, "无实例操控权限"),
    INSTALL_APP_FAIL_EXCEPTION(110025, "安装应用失败"),
    INSTANCE_PROPERTIES_NOT_SYNCHRONIZED(110026, "实例属性未同步"),
    DUPLICATE_INSTANCE_NUMBER(110027, "实例编号集合存在重复项"),
    PAD_CODE_NOT_EXIST(110028, "实例不存在"),
    PAD_CODE_NOT_BELONG_TO_CUSTOMER(110042, "存在不属于当前用户的实例"),
    DEVICE_NOT_ONLINE(110109, "实例:%s 所在的板卡为离线状态,请联系管理员"),
    CENTRAL_SERVICE_EXCEPTION(110029, "中央服务异常，请联系管理员"),
    EXECUTE_POWER_RESTART_FAIL_EXCEPTION(110030, "执行断电重启命令失败,参数请求不合规"),
    DC_NOT_EXIST(110031, "机房不存在"),
    DEVICE_IP_NOT_EXIST(110032, "物理IP不存在"),
    DEVICE_IP_NOT_ONLINE(110100, "当前在线板卡数量为0个"),
    EXECUTE_POWER_RESTART_FAIL(110033, "执行断电重启命令失败"),
    POD_OFFLINE_EXCEPTION(110034, "实例已离线"),
    PAD_IS_DELETED(110070, "存在已删除的实例"),
    PAD_IS_NOT_NET_STORAGE(110071, "存在非网存实例"),
    PAD_CANT_BOOT_ON(110072, "存在不能开机的实例,请检查实例状态"),
    COMPUTE_UNIT_NOT_ENOUGH(110073, "算力资源不足，当前剩余%s个算力单元"),
    COMPUTE_UNIT_GET_EXCEPTION(110074, "获取算力单元失败，请重试"),
    GET_PAD_IP_EXCEPTION(110075, "获取实例IP失败，请重试"),
    ARM_SERVER_IP_RANGE_NOT_EXIST(110076, "ARM服务器网段不存在"),
    GET_PAD_MAC_EXCEPTION(110077, "获取实例MAC失败，请重试"),
    NET_PAD_BOOT_ON_FAILED(110078, "网存实例批量开机失败，%s不存在"),
    COMPUTE_UNIT_NOT_RELEASED(110079, "存在未释放算力的实例"),
    PAD_CODE_ON_LOCKED(111070, "请勿重复操作，当前实例正在开机中"),
    PAD_IS_NOT_OFF(111071, "存在非关机且非开机失败状态的实例，不允许操作开机"),
    COMPUTE_UNIT_NOT_EXIST(111072, "实例未绑定算力，无法关机"),
    NET_PAD_CONTINUOUS_SHUTDOWN_FAILURE(111073, "网存实例连续关机失败，请移除后重试"),
    PAD_CANT_DELETE(111073, "非关机、删除失败状态实例不允许删除"),
    PAD_CANT_OFF(111078, "存在非运行中且非关机失败状态的实例，不允许操作关机"),
    NET_PAD_BOOT_ON_EXCEPTION(111074, "网存实例批量开机失败（系统异常）"),
    NET_PAD_V2_NOT_SUPPORT(111075, "不支持网存2.0实例"),
    PAD_CODE_OFF_LOCKED(111076, "请勿重复操作，当前实例正在关机中"),
    PAD_CODE_DEL_LOCKED(111077, "请勿重复操作，当前实例正在删除中"),
    BOOT_ON_NUMBER_LIMIT(111078, "达到集群同时开机数量上限，请稍后重试"),

    NOT_OWNED_CUSTOMER(110035, "非板卡所属客户无法操作"),
    EXECUTE_UPGRADE_IMAGE_FAIL_EXCEPTION(110037, "升级镜像任务创建失败"),
    EXECUTE_UPGRADE_IMAGE_TASK_FAIL_EXCEPTION(110038, "执行升级镜像命令失败"),
    TASK_ADDITION_FAILED(110039, "任务添加失败"),
    USER_NOT_EXIST(110040, "账号不存在"),
    IMAGE_NOT_EXIST(110041, "镜像不存在"),
    EXECUTE_VIRTUALIZE_FAIL_EXCEPTION(110043, "板卡创建实例失败"),
    SPECIFICATION_CODE_NOT_EXIST(110044, "实例规格不存在"),
    SCREEN_LAYOUT_NOT_EXIST(110045, "屏幕布局不存在"),
    NETWORK_SEGMENT_NOT_EXIST(110046, "实例网络段不存在"),
    CONTAINER_ADDITION_FAILED(110047, "容器板卡创建实例任务添加失败"),
    CONTAINER_DEVICE_DESTROY_FAILED(110047, "容器板卡删除实例任务添加失败"),
    CONTAINER_RESTART_FAILED(110048, "容器板卡重启任务添加失败"),

    DEVICE_NUMBER_INSUFFICIENT_QUANTITY  (110101, "未找到与实例规格匹配的板卡，请前往板卡列表设置规格。"),
    PAD_IP_NOT_ENOUGH(110049, "实例IP不足"),
    EDGE_CLUSTER_NOT_EXIST(110050, "边缘集群不存在"),
    BLACKLIST_NOT_EXIST(110051, "该规格不存在应用黑名单配置"),
    EXECUTE_SET_APP_BLACK_LIST_FAIL_EXCEPTION(110052, "执行设置应用黑名单指令失败"),
    PAD_NOT_RUNNING(110053, "实例未运行"),
    PAD_CONNECT_EXCEPTION(110054, "adb开启异常,请联系管理员"),
    CONNECTION_ALREADY_EXISTS( 110055, "连接已存在,请勿重复创建"),
    CONTAINER_INSTANCE_TASK_UPDATE_FAILED(110056, "容器实例任务修改失败"),
    IMAGE_EXIST(110057, "镜像存在"),
    EXECUTE_UPDATE_PROPERTIES_FAIL_EXCEPTION_BY_NEW_PAD(110058, "执行一键新机命令失败"),
    FAILED_EXECUTE_COMMAND_SET_GATEWAY(110059, "执行设置板卡网关命令失败,参数请求不合规"),
    CARD_DOES_NOT_EXIST(110060, "板卡不存在"),
    INSTANCE_NOT_ENABLED_ADB(110061, "实例未开启ADB"),
    PAD_IS_REAL(110062,"当前实例已经是真机"),
    REAL_PHONE_TEMPLATE_IS_NULL(110063,"真机模板配置为空"),
    REAL_PHONE_TEMPLATE_NOT_EXIST(110064,"当前实例中有不满足升级真机条件,请检查实例"),
    REAL_PHONE_ADI_NOT_EXIST(110099,"ADI模板不存在,请检查参数"),
    REAL_PHONE_ADI_NOT_SUPPORT_DEVICE_LEVEL(110101,"当前实例规格不支持该ADI模板,请检查参数"),
    REAL_PHONE_IMAGE_VERSION_NOT_MATCH(110102,"当前实例镜像版本与ADI模板版本不匹配,请检查参数"),
     MOUNT_VERSION_NOT_BACKUP(110064,"当前挂载方式暂不支持备份还原"),
    PARAM_REQUEST_ILLEGALITY(110065,"参数请求不合规,请参考接口文档"),
    CONTAINER_DEVICE_CBS_UPDATE_FAILED(110068, "容器板卡cbs更新任务添加失败"),
    REQUEST_LIMIT(110069,"请求过快,请稍后再试"),
    CUSTOMER_APP_CLASSIF_NOT_EXISTS(110080, "黑白名单不存在"),
    CUSTOMER_APP_CLASSIF_LIMIT_ONE(110089, "当前模式只允许配置一条同类型的配置"),
    NOW_MODE_NOT_ALLOW_ADD_PAD_CODE(110090, "当前模式为所有实例生效，不允许添加实例"),
    CUSTOMER_APP_CLASSIF_LIMIT_MODE(110091, "同类型不允许存在多个模式"),
    NOW_MODE_NOT_ALLOW_DEL_PAD_CODE(110092, "当前模式为所有实例生效，不允许删除实例"),
    PAD_MAC_ADDR_GENERATE_FAIL(110093, "实例MAC地址生成失败"),
    POD_OFFLINE_OFFLINE (110094, "没有可以关机的实例,请检查实例状态"),
    POD_OFFLINE_ONLINE (110095, "没有可以开机的实例,请检查实例状态"),
    POD_DELETE_LINE_ONLINE (110096, "当前实例状态无法删除,请检查实例状态"),
    POD_HAS_RUNNING_OFF_TASK (110097, "当前实例存在正在执行的关机任务，请稍后再试"),
    USER_DOES_NOT_EXIST(110081, "用户不存在"),
    CUSTOMER_APP_CLASSIFY_EXISTS(110082, "黑白名单已存在"),
    CUSTOMER_ID_ILLEGALITY(110083, "客户id不合法"),
    APP_CLASSIFY_NOT_EXIST_OR_DEL(110084, "该黑白名单不存在或已删除"),
    CUSTOMER_APP_CLASSIF_NAME_REPEAT(110085, "黑白名单名称重复"),
    NEW_BLACKLIST_NOT_EXIST(110086, "不存在应用黑名单配置"),
    WHITELIST_NOT_EXIST(110087, "该规格不存在应用白名单配置"),
    NEW_WHITELIST_NOT_EXIST(110088, "不存在应用白名单配置"),
    SWITCH_ROOT_PACKAGE_NOT_NULL(110089, "开启单个root包名不能为空"),
    PAD_CODE_GENERATE_FAIL(110090, "实例编号生成失败"),
    FILE_NOT_AVAILABLE_EXCEPTION(140005, "文件不可用"),

    FILE_NOT_PACKAGE_EXCEPTION(140008, "安装应用必须传包名"),
    THE_PATH_IS_INCORRECT(140006, "文件存储路径不正确"),
    PLEASE_NOT_SUBMIT_FREQUENTLY(140007, "请勿频繁操作"),
    GPS_INJECT_INFO_FAIL_EXCEPTION(110010, "设置经纬度失败"),

    DATA_DEL_NOT_EXIST(140207, "批量删除实例数据，每次最多支持200条记录"),
    GROUP_NOT_EXIST(140007, "分组不存在"),

    GROUP_ALREADY_EXIST(140008, "分组已存在"),
    GROUP_EXIST_PAD(140009, "该分组下存在实例，不能删除"),

    GROUP_MOVE_PAD_ERROR(140010, "分组移动实例失败"),

    CUSTOMER_NOT_EXIST(140011, "客户不存在"),

    GROUP_MOVE_ONLY_COMMON_POOL(140012, "只能移动公共池下的板卡实例"),

    INITIALIZATION_PARAMETERS_CANNOT_BE_EMPTY(210024, "初始化参数不能为空"),

    ARM_SERVER_IP_EXIST(210009, "ARM服务器IP重复"),

    DEVICE_SUBNET_BINDED( 210019, "板卡子网已绑定设备"),

    DEVICE_GATEWAY_DOES_NOT_EXIST(210027, "板卡网关不存在"),
    PAD_GATEWAY_DOES_NOT_EXIST(210028, "实例网关不存在"),
    CLUSTER_INFORMATION_DOES_NOT_EXIST(210002, "集群信息不存在"),

    FAILED_TO_INITIALIZE_SERVER(210007, "初始化服务器失败，请联系管理员"),
    FAILED_CREATE_SERVER(210016, "创建服务器失败，请联系管理员"),
    NAME_OR_IPV4CIDR_EXIST(210010, "名称/IPv4 CIDR重复"),
    DUPLICATE_NAME(210025, "网关重复"),
    DOWNLOAD_FILE_ERROR(210051, "文件上传失败，请重试"),
    CHASSIS_LABEL_EXIST(210034, "SN号已存在"),



    // 网存实例相关
    NETWORK_INSTANCE_CAPACITY_REDUCTION_NOT_ALLOWED_MESSAGE (220000, "不允许网存实例缩小容量,请检查实例信息"),
    NETWORK_INSTANCE_SHUTDOWN_REQUIRED_MESSAGE   (220001, "当前批次实例中存在未关机的实例，请检查实例状态"),
    NETWORK_INSTANCE_DEVICE_LEVEL_NOT_EXIST_REQUIRED_MESSAGE   (220002, "当前规格不存在,请检查参数"),
    NETWORK_INSTANCE_STORAGE_CAPACITY_NOT_EXIST_REQUIRED_MESSAGE   (220003, "暂不支持当前存储规格,请参考文档设置"),
    NETWORK_INSTANCE_NO_SERVERS_FOUND_IN_CLUSTER_CREATE_ONE_FIRST   (220004, "当前集群无服务器,请先创建服务器"),
    NETWORK_INSTANCE_REAL_NO_UPDATE_TYPE   (220005, "云真机不支持修改实例规格"),
    NETWORK_INSTANCE_DEVICE_CODE_NOT_EXIST_REQUIRED_MESSAGE   (220007, "操作的板卡不存在,请检查参数"),

    NETWORK_INSTANCE_UPDATE_PARAM_NOT_EMPTY   (220006, "参数校验失败,请参考文档"),
    NET_STORAGE_INSUFFICIENT_CAPACITY  (220009, "当前集群存储容量不足,请联系管理员"),
    NETWORK_INSTANCE_UPDATE_HAS_RUNNING  (220008, "当前板卡存在运行中的算力单元，无法修改，请参考文档"),
    NETWORK_RES_UNIT_EMPTY  (220009, "无法操作不属于自己的存储单元,请检查参数"),
    NETWORK_RES_UNIT_SHUTDOWN_ON  (220010, "处于开机中的网存无法备份,请检查数据"),
    NETWORK_RES_FREQUENT_OPERATION  (220011, "频繁操作网存实例,请稍后再试"),
    NETWORK_RES_DISABLE_THE_ACTION (220013, "禁止的操作:不允许同时操作多个用户的网存的实例。"),
    NETWORK_INSTANCE_COMPUTE_UNIT_NOT  (2200014, "当前集群下没有可用的算力资源，算力资源不足，请联系管理员处理。"),
    NETWORK_INSTANCE_RES_UNIT_INSUFFICIENT_CAPACITY  (2200015, "选择的备份数据大小超过了当前实例的可用存储空间！"),
    NETWORK_INSTANCE_STATUS_OFF  (2200016, "选择的实例处于关机状态,不支持当前操作! "),
    NETWORK_RES_UNIT_IMAGES_SHUTDOWN_ON  (2200017, "该实例安卓版本与备份包原安卓版本不一致，无法开机！"),
    NETWORK_RES_UNIT_MODEL_SHUTDOWN_ON  (2200019, "该实例安卓型号与备份包原安卓型号不一致，无法开机！"),
    NETWORK_RES_UNIT_ADI_SHUTDOWN_ON  (2200018, "该实例AID模板与备份包原品牌型号不一致，无法开机！"),

    NETWORK_RES_UNIT_TYPE_SHUTDOWN_ON  (2200020, "该实例类型与备份包原类型不一致，无法开机！ "),
    NETWORK_RES_EXECUTION_FAILED  (220012, "操作网存实例失败,请联系管理员"),
    NETWORK_DEVICE_BOARD_IMAGE_EMPTY  (220013, "板卡预热失败,没有可用的镜像或板卡,请检查配置！"),
    REAL_PHONE_ADI_UNAVAILABLE(110999,"ADI模板已被禁用,请重新选择"),
    REAL_PHONE_ADI_NOT_RIGHT(111110,"请注意，仅允许修改归属当前客户名下的模板id"),
    NET_SYNC_DATA_EXECUTING(220021,"数据备份中，请稍后再试"),
    NET_SYNC_DATA_NOT_EXISTS(220021,"当前备份ID已不存在"),
    AUDIOTOMIC_FILE_UNIQUEID_URL_NULL(220022, "请提供音频文件的唯一ID或下载地址（fileUniqueId 或 url 必须传一个）"),
    AUDIOTOMIC_FILE_UNIQUEID_NULL(220023, "fileUniqueId音频文件不存在"),
    NET_DATA_SYNC_EXECUTING(220024,"数据同步中，请稍后再试"),
    ;
    private final int status;
    private final String msg;
}
