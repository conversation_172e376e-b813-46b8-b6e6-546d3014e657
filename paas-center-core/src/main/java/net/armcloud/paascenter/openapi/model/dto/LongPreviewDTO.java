package net.armcloud.paascenter.openapi.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.armcloud.paascenter.common.model.dto.BaseDTO;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Data
public class LongPreviewDTO extends BaseDTO {

    @ApiModelProperty(value = "实例id", required = true)
    @NotNull(message = "padCodes cannot null")
    @Size(min = 1, message = "实例数量不少于1个")
    @Size(max = 100, message = "实例数量不能超过100个")
    private List<String> padCodes;

    @ApiModelProperty(value = "图片格式（PNG或JPEG），默认PNG", allowableValues = "png, jpg")
    private Format format = Format.png;  // 改成枚举类型，默认是 PNG


    /**
     * 图片高度
     */
    private String height;
    /**
     * 图片质量
     */
    private String quality;
    /**
     * 图片宽度
     */
    private String width;
    /**
     * 图片格式
     */
    public enum Format {
        png,
        jpg
    }

}
