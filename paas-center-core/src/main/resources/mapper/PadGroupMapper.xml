<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.openapi.mapper.PadGroupMapper">

    <resultMap id="padGroupResultMap" type="net.armcloud.paascenter.openapi.model.vo.PadGroupVO">
        <id column="group_id" property="groupId"/>
        <result column="group_name"  property="groupName"/>
        <result column="padCount"  property="padCount"/>
    </resultMap>

    <select id="selectPadGroupListVO" resultMap="padGroupResultMap">
        SELECT
        ppg.group_id,ppg.group_name,count(ppg.id) padCount
        FROM
        pad_group ppg ,pad
        where ppg.group_id=pad.group_id and ppg.customer_id = pad.customer_id
          and ppg.customer_id=#{customerId}
        <if test="groupIds != null and groupIds.size() > 0">
            and ppg.group_id in
            <foreach collection="groupIds" item="groupId" open="(" separator="," close=")">
                #{groupId}
            </foreach>
        </if>
        <if test="padCode != null and padCode != ''">
            and pad.pad_code=#{padCode}
        </if>
        GROUP BY ppg.id
    </select>

    <select id="countPadByGroupId" resultType="java.lang.Integer" >
        SELECT count(1) FROM pad WHERE group_id = #{id}
    </select>
    <select id="countPadByGroupIdAndGroupName" resultType="java.lang.Integer">
        SELECT count(1) FROM pad_group ppg WHERE
        ppg.group_id = #{groupId} AND ppg.group_name = #{groupName} and ppg.delete_flag = 0
    </select>


</mapper>